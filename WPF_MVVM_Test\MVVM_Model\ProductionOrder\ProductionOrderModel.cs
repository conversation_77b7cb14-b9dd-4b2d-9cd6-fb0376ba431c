using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace WPF_MVVM_Test.MVVM_Model.ProductionOrder
{
    /// <summary>
    /// 增强版生产工单数据模型 - 包含图片中显示的所有字段
    /// </summary>
    public class ProductionOrderModel : INotifyPropertyChanged
    {
        // 基础字段
        private string _id;
        private int _index;
        private bool _isSelected;

        // 任务相关字段
        private string _taskNumber;
        private string _taskName;
        private string _stationName;

        // 工单相关字段
        private string _orderNumber;
        private string _orderName;
        private string _workStationName;

        // 工艺相关字段
        private string _processFlow;
        private string _processName;
        private string _processNumber;
        private string _taskColor;

        // 数量相关字段
        private decimal _planQuantity;
        private decimal _actualQuantity;

        // 时间相关字段
        private DateTime? _planStartTime;
        private DateTime? _planEndTime;
        private string _planDuration;
        private DateTime? _actualStartTime;
        private DateTime? _actualEndTime;
        private string _actualDuration;

        // 状态相关字段
        private string _status;

        // 兼容性字段（保持与原有代码的兼容性）
        private string _productionPlanId;
        private string _productionPlan;
        private string _productId;
        private string _productName;
        private string _productNumber;
        private string _specification;
        private string _unit;

        // UI相关字段
        private string _process1Progress;
        private string _process2Progress;
        private string _process3Progress;
        private string _process4Progress;

        #region 基础属性

        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        #endregion

        #region 任务相关属性

        public string TaskNumber
        {
            get => _taskNumber;
            set => SetProperty(ref _taskNumber, value);
        }

        public string TaskName
        {
            get => _taskName;
            set => SetProperty(ref _taskName, value);
        }

        public string StationName
        {
            get => _stationName;
            set => SetProperty(ref _stationName, value);
        }

        #endregion

        #region 工单相关属性

        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        public string OrderName
        {
            get => _orderName;
            set => SetProperty(ref _orderName, value);
        }

        public string WorkStationName
        {
            get => _workStationName;
            set => SetProperty(ref _workStationName, value);
        }

        #endregion

        #region 工艺相关属性

        public string ProcessFlow
        {
            get => _processFlow;
            set => SetProperty(ref _processFlow, value);
        }

        public string ProcessName
        {
            get => _processName;
            set => SetProperty(ref _processName, value);
        }

        public string ProcessNumber
        {
            get => _processNumber;
            set => SetProperty(ref _processNumber, value);
        }

        public string TaskColor
        {
            get => _taskColor;
            set => SetProperty(ref _taskColor, value);
        }

        #endregion

        #region 数量相关属性

        public decimal PlanQuantity
        {
            get => _planQuantity;
            set => SetProperty(ref _planQuantity, value);
        }

        public decimal ActualQuantity
        {
            get => _actualQuantity;
            set => SetProperty(ref _actualQuantity, value);
        }

        #endregion

        #region 时间相关属性

        public DateTime? PlanStartTime
        {
            get => _planStartTime;
            set => SetProperty(ref _planStartTime, value);
        }

        public DateTime? PlanEndTime
        {
            get => _planEndTime;
            set => SetProperty(ref _planEndTime, value);
        }

        public string PlanDuration
        {
            get => _planDuration;
            set => SetProperty(ref _planDuration, value);
        }

        public DateTime? ActualStartTime
        {
            get => _actualStartTime;
            set => SetProperty(ref _actualStartTime, value);
        }

        public DateTime? ActualEndTime
        {
            get => _actualEndTime;
            set => SetProperty(ref _actualEndTime, value);
        }

        public string ActualDuration
        {
            get => _actualDuration;
            set => SetProperty(ref _actualDuration, value);
        }

        #endregion

        #region 状态相关属性

        public string Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanSchedule));
                    OnPropertyChanged(nameof(CanView));
                    SetProcessProgress();
                }
            }
        }

        #endregion

        #region 兼容性属性（保持与原有代码的兼容性）

        public string ProductionPlanId
        {
            get => _productionPlanId;
            set => SetProperty(ref _productionPlanId, value);
        }

        public string ProductionPlan
        {
            get => _productionPlan;
            set => SetProperty(ref _productionPlan, value);
        }

        public string ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);
        }

        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        public string Specification
        {
            get => _specification;
            set => SetProperty(ref _specification, value);
        }

        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value);
        }

        #endregion

        #region UI相关属性

        public string Process1Progress
        {
            get => _process1Progress;
            set => SetProperty(ref _process1Progress, value);
        }

        public string Process2Progress
        {
            get => _process2Progress;
            set => SetProperty(ref _process2Progress, value);
        }

        public string Process3Progress
        {
            get => _process3Progress;
            set => SetProperty(ref _process3Progress, value);
        }

        public string Process4Progress
        {
            get => _process4Progress;
            set => SetProperty(ref _process4Progress, value);
        }

        #endregion

        #region 计算属性

        public string StatusText => GetStatusText();
        public string StatusColor => GetStatusColor();
        public bool CanSchedule => Status == "待排产" || Status == "未派工";
        public bool CanView => Status != "待排产" && Status != "未派工";

        // 兼容性属性（用于UI绑定）
        public string AssociatedPlan => ProductionPlan ?? "无";
        public string ProductType => "标准产品"; // 可以根据实际需求调整
        public DateTime? DemandDate => PlanEndTime;
        public DateTime? StartTime => PlanStartTime;
        public DateTime? EndTime => PlanEndTime;

        #endregion

        #region 私有方法

        private string GetStatusText()
        {
            return Status switch
            {
                "未派工" => "未派工",
                "已下达" => "已下达",
                "进行中" => "进行中",
                "已完成" => "已完成",
                "已关闭" => "已关闭",
                "已暂停" => "已暂停",
                // 兼容原有状态
                "待排产" => "待排产",
                "未开始" => "未开始",
                _ => Status ?? "未知"
            };
        }

        private string GetStatusColor()
        {
            return Status switch
            {
                "未派工" => "#8C8C8C", // 灰色
                "已下达" => "#1890FF", // 蓝色
                "进行中" => "#52C41A", // 绿色
                "已完成" => "#FA8C16", // 橙色
                "已关闭" => "#6C757D", // 深灰色
                "已暂停" => "#FF4D4F", // 红色
                // 兼容原有状态
                "待排产" => "#8C8C8C", // 灰色
                "未开始" => "#1890FF", // 蓝色
                _ => "#8C8C8C" // 默认灰色
            };
        }

        private void SetProcessProgress()
        {
            // 根据状态设置工序进度
            switch (Status)
            {
                case "未派工":
                    Process1Progress = "待开始";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "已下达":
                    Process1Progress = "准备中";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "进行中":
                    Process1Progress = "已完成";
                    Process2Progress = "进行中";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "已完成":
                    Process1Progress = "已完成";
                    Process2Progress = "已完成";
                    Process3Progress = "已完成";
                    Process4Progress = "已完成";
                    break;
                case "已暂停":
                    Process1Progress = "已完成";
                    Process2Progress = "已暂停";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "已关闭":
                    Process1Progress = "已关闭";
                    Process2Progress = "已关闭";
                    Process3Progress = "已关闭";
                    Process4Progress = "已关闭";
                    break;
                // 兼容原有状态
                case "待排产":
                    Process1Progress = "待开始";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "未开始":
                    Process1Progress = "准备中";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                default:
                    Process1Progress = "待开始";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
            }
        }

        #endregion

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}