using System;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.WorkOrderTask
{
    /// <summary>
    /// 工单任务API数据模型 - 对应后端API返回的数据结构
    /// </summary>
    public class WorkOrderTaskApiModel
    {
        /// <summary>
        /// 工单任务ID
        /// </summary>
        [JsonPropertyName("workOrderTaskId")]
        public string WorkOrderTaskId { get; set; }

        /// <summary>
        /// 检验项名称
        /// </summary>
        [JsonPropertyName("inspectionName")]
        public string InspectionName { get; set; }

        /// <summary>
        /// 检验类型
        /// </summary>
        [JsonPropertyName("inspectionType")]
        public string InspectionType { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        [JsonPropertyName("productId")]
        public string ProductId { get; set; }

        /// <summary>
        /// 工艺步骤ID
        /// </summary>
        [JsonPropertyName("processStepId")]
        public string ProcessStepId { get; set; }

        /// <summary>
        /// 站点ID
        /// </summary>
        [JsonPropertyName("stationId")]
        public string StationId { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        [JsonPropertyName("reportId")]
        public string ReportId { get; set; }

        /// <summary>
        /// 检验员ID
        /// </summary>
        [JsonPropertyName("inspectorId")]
        public string InspectorId { get; set; }

        /// <summary>
        /// 报告数量
        /// </summary>
        [JsonPropertyName("reportQuantity")]
        public int ReportQuantity { get; set; }

        /// <summary>
        /// 报告时间
        /// </summary>
        [JsonPropertyName("reportTime")]
        public string ReportTime { get; set; }

        /// <summary>
        /// 检验时间
        /// </summary>
        [JsonPropertyName("inspectionTime")]
        public string InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        [JsonPropertyName("inspectionDepartment")]
        public string InspectionDepartment { get; set; }

        /// <summary>
        /// 测试数量
        /// </summary>
        [JsonPropertyName("testQuantity")]
        public int TestQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [JsonPropertyName("qualifiedQuantity")]
        public int QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [JsonPropertyName("unqualifiedQuantity")]
        public int UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 总体结果
        /// </summary>
        [JsonPropertyName("overallResult")]
        public string OverallResult { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 报工请求模型
    /// </summary>
    public class WorkReportRequest
    {
        /// <summary>
        /// 工单任务ID
        /// </summary>
        [JsonPropertyName("workOrderTaskId")]
        public string WorkOrderTaskId { get; set; }

        /// <summary>
        /// 检验项名称
        /// </summary>
        [JsonPropertyName("inspectionName")]
        public string InspectionName { get; set; }

        /// <summary>
        /// 检验类型
        /// </summary>
        [JsonPropertyName("inspectionType")]
        public string InspectionType { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        [JsonPropertyName("productId")]
        public string ProductId { get; set; }

        /// <summary>
        /// 工艺步骤ID
        /// </summary>
        [JsonPropertyName("processStepId")]
        public string ProcessStepId { get; set; }

        /// <summary>
        /// 站点ID
        /// </summary>
        [JsonPropertyName("stationId")]
        public string StationId { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        [JsonPropertyName("reportId")]
        public string ReportId { get; set; }

        /// <summary>
        /// 检验员ID
        /// </summary>
        [JsonPropertyName("inspectorId")]
        public string InspectorId { get; set; }

        /// <summary>
        /// 报告数量
        /// </summary>
        [JsonPropertyName("reportQuantity")]
        public int ReportQuantity { get; set; }

        /// <summary>
        /// 报告时间
        /// </summary>
        [JsonPropertyName("reportTime")]
        public string ReportTime { get; set; }

        /// <summary>
        /// 检验时间
        /// </summary>
        [JsonPropertyName("inspectionTime")]
        public string InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        [JsonPropertyName("inspectionDepartment")]
        public string InspectionDepartment { get; set; }

        /// <summary>
        /// 测试数量
        /// </summary>
        [JsonPropertyName("testQuantity")]
        public int TestQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [JsonPropertyName("qualifiedQuantity")]
        public int QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [JsonPropertyName("unqualifiedQuantity")]
        public int UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 总体结果
        /// </summary>
        [JsonPropertyName("overallResult")]
        public string OverallResult { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; }
    }
}
