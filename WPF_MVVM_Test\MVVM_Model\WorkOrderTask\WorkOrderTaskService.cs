using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace WPF_MVVM_Test.MVVM_Model.WorkOrderTask
{
    /// <summary>
    /// 工单任务服务 - 提供工单任务的数据操作
    /// </summary>
    public class WorkOrderTaskService
    {
        #region 模拟数据

        /// <summary>
        /// 获取模拟的工单任务数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<WorkOrderTaskModel>> GetWorkOrderTasksAsync()
        {
            // 模拟异步操作
            await Task.Delay(100);

            var tasks = new List<WorkOrderTaskModel>();
            var random = new Random();
            var statuses = new[] { "未派工", "已下达", "进行中", "已完成", "已暂停" };
            var taskNames = new[] { "生产工件任务1", "生产工件任务2", "生产工件任务3", "装配任务", "检测任务", "包装任务" };
            var stations = new[] { "站点一", "站点二", "站点三", "站点四", "站点五" };

            for (int i = 1; i <= 50; i++)
            {
                var planStartTime = DateTime.Now.AddDays(-random.Next(0, 30)).AddHours(random.Next(8, 18));
                var actualStartTime = planStartTime.AddMinutes(random.Next(-30, 60));
                var planDurationHours = random.Next(1, 8);
                var actualDurationHours = planDurationHours + random.Next(-1, 3);

                tasks.Add(new WorkOrderTaskModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Index = i,
                    TaskNumber = $"RWBH{i:D4}",
                    TaskName = taskNames[random.Next(taskNames.Length)],
                    StationName = stations[random.Next(stations.Length)],
                    PlanStartTime = planStartTime,
                    ActualStartTime = actualStartTime,
                    PlanDuration = $"{planDurationHours}小时",
                    ActualEndTime = actualStartTime.AddHours(actualDurationHours),
                    ActualDuration = $"{actualDurationHours}小时{random.Next(0, 60)}分钟",
                    Status = statuses[random.Next(statuses.Length)]
                });
            }

            return tasks;
        }

        /// <summary>
        /// 根据条件搜索工单任务
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        /// <param name="status">状态筛选</param>
        /// <param name="station">站点筛选</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<WorkOrderTaskModel>> SearchWorkOrderTasksAsync(
            string searchText = null,
            string status = null,
            string station = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var allTasks = await GetWorkOrderTasksAsync();

            var filteredTasks = allTasks.AsQueryable();

            // 文本搜索
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filteredTasks = filteredTasks.Where(t =>
                    t.TaskNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    t.TaskName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    t.StationName.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            }

            // 状态筛选
            if (!string.IsNullOrWhiteSpace(status) && status != "全部")
            {
                filteredTasks = filteredTasks.Where(t => t.Status == status);
            }

            // 站点筛选
            if (!string.IsNullOrWhiteSpace(station) && station != "全部")
            {
                filteredTasks = filteredTasks.Where(t => t.StationName == station);
            }

            // 日期筛选
            if (startDate.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.PlanStartTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                filteredTasks = filteredTasks.Where(t => t.PlanStartTime <= endDate.Value);
            }

            return filteredTasks.ToList();
        }

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="newStatus">新状态</param>
        /// <returns></returns>
        public async Task<bool> UpdateTaskStatusAsync(string taskId, string newStatus)
        {
            // 模拟异步操作
            await Task.Delay(50);

            // 这里应该是实际的数据库更新操作
            // 目前只是模拟返回成功
            return true;
        }

        /// <summary>
        /// 开工任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns></returns>
        public async Task<bool> StartTaskAsync(string taskId)
        {
            return await UpdateTaskStatusAsync(taskId, "进行中");
        }

        /// <summary>
        /// 完工任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns></returns>
        public async Task<bool> CompleteTaskAsync(string taskId)
        {
            return await UpdateTaskStatusAsync(taskId, "已完成");
        }

        /// <summary>
        /// 暂停任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns></returns>
        public async Task<bool> PauseTaskAsync(string taskId)
        {
            return await UpdateTaskStatusAsync(taskId, "已暂停");
        }

        /// <summary>
        /// 获取所有状态选项
        /// </summary>
        /// <returns></returns>
        public List<string> GetStatusOptions()
        {
            return new List<string> { "全部", "未派工", "已下达", "进行中", "已完成", "已暂停", "已关闭" };
        }

        /// <summary>
        /// 获取所有站点选项
        /// </summary>
        /// <returns></returns>
        public List<string> GetStationOptions()
        {
            return new List<string> { "全部", "站点一", "站点二", "站点三", "站点四", "站点五" };
        }

        #endregion
    }
}
