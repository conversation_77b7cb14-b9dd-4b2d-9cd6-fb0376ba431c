using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WPF_MVVM_Test.MVVM_Model.Common;
using WPF_MVVM_Test.MVVM_Model.ProductionOrder;

namespace WPF_MVVM_Test.MVVM_Services
{
    /// <summary>
    /// 模拟生产工单服务 - 生成符合图片要求的测试数据
    /// </summary>
    public class MockProductionOrderService
    {
        private readonly List<ProductionOrderModel> _mockData;
        private readonly Random _random;

        public MockProductionOrderService()
        {
            _random = new Random();
            _mockData = GenerateMockData();
        }

        /// <summary>
        /// 获取生产工单列表
        /// </summary>
        public async Task<ApiResponse<PagedData<ProductionOrderModel>>> GetProductionOrderListAsync(
            string orderNumber = null,
            string productName = null,
            string status = null,
            int pageIndex = 1,
            int pageSize = 10)
        {
            await Task.Delay(500); // 模拟网络延迟

            try
            {
                var filteredData = _mockData.AsQueryable();

                // 应用筛选条件
                if (!string.IsNullOrEmpty(orderNumber))
                {
                    filteredData = filteredData.Where(x => x.OrderNumber.Contains(orderNumber, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(productName))
                {
                    filteredData = filteredData.Where(x => x.TaskName.Contains(productName, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(status))
                {
                    filteredData = filteredData.Where(x => x.Status == status);
                }

                var totalCount = filteredData.Count();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var pagedData = filteredData
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                // 设置索引
                for (int i = 0; i < pagedData.Count; i++)
                {
                    pagedData[i].Index = (pageIndex - 1) * pageSize + i + 1;
                }

                return new ApiResponse<PagedData<ProductionOrderModel>>
                {
                    IsSuc = true,
                    Code = 200,
                    Msg = "获取成功",
                    Data = new PagedData<ProductionOrderModel>
                    {
                        Data = pagedData,
                        TotalCount = totalCount,
                        TotalPage = totalPages
                    }
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<PagedData<ProductionOrderModel>>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"获取生产工单列表失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 生成模拟数据
        /// </summary>
        private List<ProductionOrderModel> GenerateMockData()
        {
            var data = new List<ProductionOrderModel>();
            var statuses = new[] { "未派工", "已下达", "进行中", "已完成", "已关闭", "已暂停" };
            var taskColors = new[] { "#1890FF", "#52C41A", "#FA8C16", "#FF4D4F", "#722ED1", "#13C2C2" };
            var stationNames = new[] { "站点一", "站点二", "站点三", "站点四", "站点五" };
            var processFlows = new[] { "工序一", "工序二", "工序三", "工序四" };

            for (int i = 1; i <= 100; i++)
            {
                var baseDate = DateTime.Now.AddDays(-_random.Next(0, 30));
                var planStartTime = baseDate.AddHours(_random.Next(8, 18));
                var planEndTime = planStartTime.AddHours(_random.Next(1, 8));
                var actualStartTime = _random.NextDouble() > 0.3 ? planStartTime.AddMinutes(_random.Next(-30, 60)) : (DateTime?)null;
                var actualEndTime = actualStartTime?.AddHours(_random.Next(1, 10));

                var status = statuses[_random.Next(statuses.Length)];
                var planQuantity = _random.Next(10, 200);
                var actualQuantity = status == "已完成" ? planQuantity : _random.Next(0, planQuantity);

                data.Add(new ProductionOrderModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Index = i,
                    TaskNumber = $"RWBH{i:D4}",
                    TaskName = $"生产工件任务{i}",
                    StationName = stationNames[_random.Next(stationNames.Length)],
                    OrderNumber = $"GDBH{i:D4}",
                    OrderName = $"新产品工单",
                    WorkStationName = stationNames[_random.Next(stationNames.Length)],
                    ProcessFlow = processFlows[_random.Next(processFlows.Length)],
                    ProcessName = $"GXBH{i:D4}",
                    ProcessNumber = $"GX{i:D3}",
                    TaskColor = taskColors[_random.Next(taskColors.Length)],
                    PlanQuantity = planQuantity,
                    ActualQuantity = actualQuantity,
                    PlanStartTime = planStartTime,
                    PlanEndTime = planEndTime,
                    PlanDuration = CalculateDuration(planStartTime, planEndTime),
                    ActualStartTime = actualStartTime,
                    ActualEndTime = actualEndTime,
                    ActualDuration = actualStartTime != null && actualEndTime != null ? 
                        CalculateDuration(actualStartTime.Value, actualEndTime.Value) : "",
                    Status = status,
                    // 兼容性字段
                    ProductName = $"生产工件任务{i}",
                    ProductNumber = $"RWBH{i:D4}",
                    Unit = "个",
                    Specification = "标准规格"
                });
            }

            return data;
        }

        /// <summary>
        /// 计算时长
        /// </summary>
        private string CalculateDuration(DateTime startTime, DateTime endTime)
        {
            var duration = endTime - startTime;
            if (duration.TotalDays >= 1)
            {
                return $"{(int)duration.TotalDays}天{duration.Hours}小时";
            }
            else if (duration.TotalHours >= 1)
            {
                return $"{(int)duration.TotalHours}小时{duration.Minutes}分钟";
            }
            else
            {
                return $"{duration.Minutes}分钟";
            }
        }

        /// <summary>
        /// 开始生产工单
        /// </summary>
        public async Task<ApiResponse<object>> StartProductionOrderAsync(string orderId)
        {
            await Task.Delay(200);
            
            var order = _mockData.FirstOrDefault(x => x.Id == orderId);
            if (order != null)
            {
                order.Status = "进行中";
                order.ActualStartTime = DateTime.Now;
            }

            return new ApiResponse<object>
            {
                IsSuc = true,
                Code = 200,
                Msg = "开始成功"
            };
        }

        /// <summary>
        /// 取消生产工单
        /// </summary>
        public async Task<ApiResponse<object>> CancelProductionOrderAsync(string orderId)
        {
            await Task.Delay(200);
            
            var order = _mockData.FirstOrDefault(x => x.Id == orderId);
            if (order != null)
            {
                order.Status = "已关闭";
            }

            return new ApiResponse<object>
            {
                IsSuc = true,
                Code = 200,
                Msg = "取消成功"
            };
        }

        /// <summary>
        /// 结束生产工单
        /// </summary>
        public async Task<ApiResponse<object>> EndProductionOrderAsync(string orderId)
        {
            await Task.Delay(200);
            
            var order = _mockData.FirstOrDefault(x => x.Id == orderId);
            if (order != null)
            {
                order.Status = "已完成";
                order.ActualEndTime = DateTime.Now;
                order.ActualQuantity = order.PlanQuantity;
            }

            return new ApiResponse<object>
            {
                IsSuc = true,
                Code = 200,
                Msg = "结束成功"
            };
        }

        /// <summary>
        /// 撤回生产工单
        /// </summary>
        public async Task<ApiResponse<object>> RecallProductionOrderAsync(string orderId)
        {
            await Task.Delay(200);
            
            var order = _mockData.FirstOrDefault(x => x.Id == orderId);
            if (order != null)
            {
                order.Status = "未派工";
                order.ActualStartTime = null;
                order.ActualEndTime = null;
                order.ActualQuantity = 0;
            }

            return new ApiResponse<object>
            {
                IsSuc = true,
                Code = 200,
                Msg = "撤回成功"
            };
        }

        public void Dispose()
        {
            // 模拟服务无需释放资源
        }
    }
}
