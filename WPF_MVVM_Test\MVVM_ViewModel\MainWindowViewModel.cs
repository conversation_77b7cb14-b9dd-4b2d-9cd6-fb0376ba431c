using System;
using System.Windows.Input;
using System.Collections.ObjectModel;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model;
using WPF_MVVM_Test.MVVM_ViewModel.Bom;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;
using WPF_MVVM_Test.Services;
using WPF_MVVM_Test.MVVM_ViewModel.ProductionOrder;

namespace WPF_MVVM_Test.MVVM_ViewModel
{
    /// <summary>
    /// 主窗口的ViewModel - MVVM模式中的ViewModel层，登录界面的"大脑"
    ///
    /// 🎯 这个类的作用（用餐厅例子理解）：
    /// 想象一个餐厅：
    /// - View（界面）= 餐厅的前台，客人看到的菜单、收银台
    /// - Model（数据）= 后厨的食材、菜谱
    /// - ViewModel（这个类）= 服务员，连接前台和后厨
    ///
    /// 服务员（ViewModel）的工作：
    /// 1. 接收客人订单（用户输入用户名密码）
    /// 2. 检查菜品是否可做（验证输入是否有效）
    /// 3. 传达给后厨（调用业务逻辑）
    /// 4. 把结果告诉客人（显示登录成功或失败）
    ///
    /// 🔧 具体技术职责：
    /// 1. 作为View和Model之间的桥梁 - 连接界面和数据
    /// 2. 处理所有的业务逻辑（登录验证） - 决定什么时候做什么
    /// 3. 管理界面状态和用户交互 - 控制按钮是否可点击等
    /// 4. 提供数据绑定的属性和命令 - 让界面能够自动更新
    ///
    /// 🎉 为什么继承BaseViewModel？
    /// 就像员工入职要接受公司培训一样：
    /// 1. 获得属性变更通知功能 - 学会如何与界面沟通
    /// 2. 支持WPF的数据绑定机制 - 掌握公司的工作流程
    /// 3. 使用基类提供的工具方法 - 使用公司提供的办公软件
    /// 4. 遵循统一的代码规范 - 按照公司标准工作
    /// </summary>
    public class MainWindowViewModel : BaseViewModel
    {
        // 私有字段：存储Account模型实例
        // 🎯 为什么用私有字段？
        // 就像个人钱包，只有自己能直接碰，别人要用钱得通过你（属性）
        // 这样可以控制数据的访问和修改，确保数据安全
        private Account _currentAccount;

        // 私有字段：存储消息文本
        // 🎯 为什么初始化为string.Empty？
        // 避免null值，防止界面显示时出现异常
        // 就像准备一个空盒子，总比没有盒子好
        private string _message = string.Empty;

        /// <summary>
        /// 构造函数：初始化ViewModel - 这是ViewModel的"出生证明"
        ///
        /// 🎯 构造函数的作用（用搬新家例子理解）：
        /// 就像搬进新房子要做的准备工作：
        /// 1. 准备家具（创建Account实例）
        /// 2. 安装电器（创建LoginCommand）
        /// 3. 确保一切就绪，可以正常生活（ViewModel可以正常工作）
        ///
        /// 🔧 为什么在这里初始化？
        /// - 确保ViewModel一创建就有完整的功能
        /// - 避免后续使用时出现null引用异常
        /// - 遵循"创建即可用"的设计原则
        ///
        /// 📝 初始化内容解释：
        /// - new Account()：创建一个空的用户账户对象
        /// - CreateCommand()：使用基类提供的便利方法创建命令
        /// </summary>
        public MainWindowViewModel()
        {
            // 创建一个新的Account实例，用于存储用户输入的账户信息
            _currentAccount = new Account();

            // 初始化用户列表
            Users = new ObservableCollection<User>();

            // 初始化用户API服务
            _userService = new UserService();

            // 初始化报表管理ViewModel
            ReportManagementViewModel = new ReportManagementViewModel();
            // 初始化新增/编辑生产计划ViewModel
            AddProductPlanViewModel = new AddProductPlanViewModel();

            // 初始化生产工单ViewModel
            ProductionOrderViewModel = new ProductionOrderViewModel();

            // 初始化AI聊天ViewModel
            ChatViewModel = new ChatViewModel();

            // 初始化数据导出ViewModel
            ExportViewModel = new ExportViewModel();

            // 创建登录命令，绑定执行方法和可执行判断方法
            // ExecuteLogin：点击登录按钮时要执行的方法
            // CanExecuteLogin：判断登录按钮是否应该启用的方法
            LoginCommand = CreateCommand(ExecuteLogin, CanExecuteLogin);

            // 创建填充测试数据命令
            // FillTestData：点击按钮时填充测试数据的方法
            // 不需要CanExecute判断，因为这个按钮总是可以点击
            FillTestDataCommand = CreateCommand(FillTestData);

            // 创建主页面菜单命令 - 超级简单的一行代码实现
            ShowHomeCommand = CreateCommand(() => CurrentPage = "首页");
            ShowUserCommand = CreateCommand(() => CurrentPage = "用户管理");
            ShowReportCommand = CreateCommand(() => CurrentPage = "报表管理");
            ShowProductPlanCommand = CreateCommand(() => CurrentPage = "生产计划");
            ShowBomCommand = CreateCommand(() => CurrentPage = "Bom管理");
            ShowMaterialCommand = CreateCommand(() => CurrentPage = "物料管理");
            ShowAddProductPlanCommand = CreateCommand(() => CurrentPage = "新增生产计划");
            ShowProductOrderCommand = CreateCommand(() => CurrentPage = "生产工单");
            ShowAiChatCommand = CreateCommand(() => CurrentPage = "AI聊天");
            ShowDataExportCommand = CreateCommand(() => CurrentPage = "数据导出");
            ShowProcessRouteCommand = CreateCommand(() => CurrentPage = "工艺路线管理");
            ShowSettingsCommand = CreateCommand(() => CurrentPage = "设置");
            LogoutCommand = CreateCommand(ExecuteLogout);

            // 创建刷新用户列表命令
            RefreshUsersCommand = CreateCommand(LoadUsersFromApi);

            // 订阅生产计划页面的导航事件
            ProductPlanViewModel.RequestNavigateToAddPage += () => CurrentPage = "新增生产计划";

            // 订阅BOM管理页面的导航事件
            BomManagementViewModel.RequestNavigateToPage += (pageName) => CurrentPage = pageName;

            // 初始化时加载真实API数据
            LoadUsersFromApi();
        }

        /// <summary>
        /// 用户名属性 - 界面和数据之间的"传话筒"
        ///
        /// 🎯 这个属性的作用（用快递例子理解）：
        /// 就像快递代收点：
        /// - get：有人问"用户名是什么？"，我去Account里查一下告诉你
        /// - set：有人说"用户名改成XXX"，我先检查是否真的变了，再更新并通知大家
        ///
        /// 🔧 为什么要这样设计？
        /// 1. 封装性：外界不能直接访问_currentAccount，必须通过这个属性
        /// 2. 数据绑定：界面的TextBox可以直接绑定到这个属性
        /// 3. 自动通知：值变化时自动通知界面更新
        /// 4. 性能优化：相同值不会触发不必要的更新
        ///
        /// 📝 工作流程：
        /// 用户在界面输入 → set方法被调用 → 检查值是否变化 → 更新数据 → 通知界面
        /// </summary>
        public string UserName
        {
            // get访问器：当有人读取UserName属性时，返回Account中的AccountName
            get => _currentAccount.AccountName;
            set
            {
                // 性能优化：只有当新值与当前值不同时才进行更新
                // 这样避免了不必要的UI刷新和事件触发
                if (_currentAccount.AccountName != value)
                {
                    // 更新Account中的实际数据
                    _currentAccount.AccountName = value;
                    // 通知所有绑定到这个属性的UI控件：数据变了，请更新显示！
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 密码属性 - 与用户名属性类似的"传话筒"，但更注重安全
        ///
        /// 🎯 这个属性的特殊之处：
        /// 密码比用户名更敏感，但在这个示例中处理方式相同
        /// 在实际项目中，密码通常需要：
        /// - 加密存储（不以明文保存）
        /// - 特殊的验证规则（长度、复杂度等）
        /// - 安全的传输方式
        ///
        /// 🔧 当前的设计：
        /// 与UserName属性完全相同的模式：
        /// 1. 封装访问Account中的密码数据
        /// 2. 提供数据绑定支持
        /// 3. 自动通知界面更新
        /// 4. 性能优化（避免重复更新）
        ///
        /// ⚠️ 注意：这只是演示代码
        /// 生产环境中密码处理会更复杂和安全
        /// </summary>
        public string Password
        {
            // 返回Account中存储的密码
            get => _currentAccount.AccountPwd;
            set
            {
                // 同样的优化：只有值真正改变时才更新
                if (_currentAccount.AccountPwd != value)
                {
                    // 更新Account中的密码数据
                    _currentAccount.AccountPwd = value;
                    // 通知界面：密码数据变了！
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 消息属性 - 使用基类的SetProperty方法，展示更优雅的写法
        ///
        /// 🎯 这个属性的作用：
        /// 就像手机的通知栏，用来显示各种提示信息：
        /// - "登录成功！欢迎XXX"
        /// - "用户名或密码错误"
        /// - 其他系统消息
        ///
        /// 🔧 为什么用SetProperty而不是手动实现？
        /// 对比两种写法：
        ///
        /// 手动方式（像UserName那样）：
        /// set {
        ///     if (_message != value) {
        ///         _message = value;
        ///         OnPropertyChanged();
        ///     }
        /// }
        ///
        /// 使用SetProperty（当前方式）：
        /// set => SetProperty(ref _message, value);
        ///
        /// 🎉 SetProperty的优势：
        /// 1. 代码更简洁：一行搞定所有逻辑
        /// 2. 不易出错：不会忘记调用OnPropertyChanged
        /// 3. 统一标准：所有属性都用同样的方式
        /// 4. 自动优化：基类已经处理了性能优化
        /// </summary>
        public string Message
        {
            get => _message;
            // 使用基类提供的SetProperty方法，自动处理所有细节
            // ref _message：传递字段的引用，让SetProperty能直接修改它
            // value：新的属性值
            set => SetProperty(ref _message, value);
        }

        /// <summary>
        /// 登录命令 - 连接界面按钮和业务逻辑的"桥梁"
        ///
        /// 🎯 什么是Command？（初学者必读）
        /// 在WPF中，Command是一种特殊的设计模式：
        /// - 传统方式：按钮点击 → 直接执行代码
        /// - Command方式：按钮点击 → 触发Command → 执行ViewModel中的方法
        ///
        /// 🔧 Command的优势：
        /// 1. 业务逻辑集中在ViewModel中，不在界面代码里
        /// 2. 支持自动启用/禁用（按钮灰化）
        /// 3. 可以绑定到多个控件（按钮、菜单项等）
        /// 4. 便于单元测试和维护
        ///
        /// 📝 这个LoginCommand的工作流程：
        /// 用户点击登录按钮 → WPF检查CanExecuteLogin → 如果返回true则调用ExecuteLogin
        /// </summary>
        public ICommand LoginCommand { get; }

        /// <summary>
        /// 填充测试数据命令 - 开发调试用的便利功能
        ///
        /// 🎯 这个命令的作用：
        /// 点击按钮后自动填入测试用的用户名和密码，方便开发和测试
        /// 就像浏览器的"记住密码"功能，但这个是专门用于开发测试的
        ///
        /// 🔧 为什么需要这个功能？
        /// 1. 开发时不用每次都手动输入测试数据
        /// 2. 提高测试效率
        /// 3. 避免输入错误
        /// 4. 方便演示功能
        /// </summary>
        public ICommand FillTestDataCommand { get; }

        /// <summary>
        /// 是否已登录 - 控制显示登录页还是主页
        /// true: 显示主页面
        /// false: 显示登录页面
        /// </summary>
        private bool _isLoggedIn = false;
        public bool IsLoggedIn
        {
            get => _isLoggedIn;
            set => SetProperty(ref _isLoggedIn, value);
        }

        /// <summary>
        /// 当前选中的页面 - 控制右侧显示什么内容
        /// 默认显示首页
        /// </summary>
        private string _currentPage = "首页";
        public string CurrentPage
        {
            get => _currentPage;
            set
            {
                SetProperty(ref _currentPage, value);
                // 通知页面可见性属性更新
                OnPropertyChanged(nameof(IsHomePageVisible));
                OnPropertyChanged(nameof(IsUserPageVisible));
                OnPropertyChanged(nameof(IsReportPageVisible));
                OnPropertyChanged(nameof(IsProductPlanPageVisible));
                OnPropertyChanged(nameof(IsBomPageVisible));
                OnPropertyChanged(nameof(IsBomAddPageVisible));
                OnPropertyChanged(nameof(IsMaterialPageVisible));
                OnPropertyChanged(nameof(IsAddProductPlanPageVisible));
                OnPropertyChanged(nameof(IsProductOrderPageVisible));
                OnPropertyChanged(nameof(IsAiChatPageVisible));
                OnPropertyChanged(nameof(IsDataExportPageVisible));
                OnPropertyChanged(nameof(IsProcessRoutePageVisible));
                OnPropertyChanged(nameof(IsSettingsPageVisible));
            }
        }

        /// <summary>
        /// 主页面菜单命令 - 超级简单的实现
        /// </summary>
        public ICommand ShowHomeCommand { get; }
        public ICommand ShowUserCommand { get; }
        public ICommand ShowReportCommand { get; }
        public ICommand ShowSettingsCommand { get; }
        public ICommand ShowProductPlanCommand { get; }
        public ICommand ShowBomCommand { get; }
        public ICommand ShowMaterialCommand { get; }
        public ICommand ShowAddProductPlanCommand { get; }
        public ICommand ShowProductOrderCommand { get; }
        public ICommand ShowAiChatCommand { get; }
        public ICommand ShowDataExportCommand { get; }
        public ICommand ShowProcessRouteCommand { get; }

        public ICommand LogoutCommand { get; }

        /// <summary>
        /// 用户列表 - 用于用户管理页面显示
        /// 使用ObservableCollection自动通知界面更新
        /// </summary>
        public ObservableCollection<User> Users { get; }

        /// <summary>
        /// 用户API服务
        /// </summary>
        private readonly UserService _userService;

        /// <summary>
        /// 报表管理ViewModel实例
        /// </summary>
        public ReportManagementViewModel ReportManagementViewModel { get; }
        /// <summary>
        /// 新增/编辑生产计划ViewModel
        /// </summary>
        public AddProductPlanViewModel AddProductPlanViewModel { get; }

        /// <summary>
        /// 生产工单ViewModel
        /// </summary>
        public ProductionOrderViewModel ProductionOrderViewModel { get; }

        /// AI聊天ViewModel实例
        /// </summary>
        public ChatViewModel ChatViewModel { get; }

        /// <summary>
        /// 数据导出ViewModel实例
        /// </summary>
        public ExportViewModel ExportViewModel { get; }

        /// <summary>
        /// 刷新用户列表命令
        /// </summary>
        public ICommand RefreshUsersCommand { get; }

        /// <summary>
        /// 首页是否可见
        /// </summary>
        public bool IsHomePageVisible => CurrentPage == "首页";

        /// <summary>
        /// 用户管理页面是否可见
        /// </summary>
        public bool IsUserPageVisible => CurrentPage == "用户管理";

        /// <summary>
        /// 报表管理页面是否可见
        /// </summary>
        public bool IsReportPageVisible => CurrentPage == "报表管理";

        /// <summary>
        /// 生产计划页面是否可见
        /// </summary>
        public bool IsProductPlanPageVisible => CurrentPage == "生产计划";

        /// <summary>
        /// Bom管理页面是否可见
        /// </summary>
        public bool IsBomPageVisible => CurrentPage == "Bom管理";

        /// <summary>
        /// BOM新增页面是否可见
        /// </summary>
        public bool IsBomAddPageVisible => CurrentPage == "BOM新增";

        /// <summary>
        /// 物料管理页面是否可见
        /// </summary>
        public bool IsMaterialPageVisible => CurrentPage == "物料管理";

        /// <summary>
        /// 新增生产计划页面是否可见
        /// </summary>
        public bool IsAddProductPlanPageVisible => CurrentPage == "新增生产计划";
        /// <summary>
        /// 生产工单页面是否可见
        /// </summary>
        public bool IsProductOrderPageVisible => CurrentPage == "生产工单";
        /// AI聊天页面是否可见
        /// </summary>
        public bool IsAiChatPageVisible => CurrentPage == "AI聊天";

        /// <summary>
        /// 数据导出页面是否可见
        /// </summary>
        public bool IsDataExportPageVisible => CurrentPage == "数据导出";

        /// <summary>
        /// 工艺路线管理页面是否可见
        /// </summary>
        public bool IsProcessRoutePageVisible => CurrentPage == "工艺路线管理";

        /// <summary>
        /// 系统设置页面是否可见
        /// </summary>
        public bool IsSettingsPageVisible => CurrentPage == "设置";




        /// <summary>
        /// 判断登录命令是否可执行 - 按钮的"智能开关"
        ///
        /// 🎯 这个方法的作用（用电梯例子理解）：
        /// 就像电梯的楼层按钮：
        /// - 电梯在1楼时，1楼按钮是灰的（不可按）
        /// - 电梯在其他楼层时，1楼按钮是亮的（可以按）
        ///
        /// 这个方法决定登录按钮什么时候可以点击：
        /// - 用户名和密码都填了 → 按钮亮起，可以点击
        /// - 有任何一个没填 → 按钮变灰，不能点击
        ///
        /// 🔧 技术细节：
        /// - WPF会自动调用这个方法来决定按钮状态
        /// - 当UserName或Password属性变化时，按钮状态会自动更新
        /// - 这提供了很好的用户体验，用户一看就知道能不能点击
        ///
        /// 🎉 用户体验提升：
        /// 用户不用试着点击按钮才知道能不能登录，界面直接告诉他们！
        /// </summary>
        private bool CanExecuteLogin()
        {
            // 检查用户名和密码是否都不为空
            // !string.IsNullOrEmpty() 表示"不是null也不是空字符串"
            return !string.IsNullOrEmpty(UserName) && !string.IsNullOrEmpty(Password);
        }

        /// <summary>
        /// 执行登录的方法 - 真正的业务逻辑处理中心
        ///
        /// 🎯 这个方法的作用（用银行ATM例子理解）：
        /// 就像ATM机的验证过程：
        /// 1. 接收用户输入的卡号和密码（UserName和Password）
        /// 2. 与银行数据库核对（这里简化为硬编码验证）
        /// 3. 返回结果给用户（设置Message属性显示结果）
        ///
        /// 🔧 当前的简化逻辑：
        /// - 用户名是"admin"且密码是"123456" → 登录成功
        /// - 其他任何组合 → 登录失败
        ///
        /// 🚀 在实际项目中，这里会：
        /// 1. 调用Web API验证用户身份
        /// 2. 处理网络异常和超时
        /// 3. 保存登录状态和用户信息
        /// 4. 跳转到主界面
        /// 5. 记录登录日志
        ///
        /// 📝 为什么不需要再次验证输入？
        /// 因为有CanExecuteLogin方法把关，只有输入完整时这个方法才会被调用
        /// 这是Command模式的优势：职责分离，逻辑清晰
        /// </summary>
        private async void ExecuteLogin()
        {
            // 第一步：验证输入
            if (string.IsNullOrWhiteSpace(UserName))
            {
                Message = "请输入用户名";
                return;
            }

            if (string.IsNullOrWhiteSpace(Password))
            {
                Message = "请输入密码";
                return;
            }

            try
            {
                // 显示加载状态
                Message = "正在登录...";

                // 第三步：调用登录方法
                bool loginSuccess = await DoLoginAsync(UserName, Password);

                // 第四步：处理登录结果
                if (loginSuccess)
                {
                    // 登录成功后切换到主页面
                    IsLoggedIn = true;
                    CurrentPage = "首页";
                    Message = "登录成功！欢迎使用系统";
                }
                // 登录失败的消息已在DoLoginAsync中设置
            }
            catch (Exception)
            {
                // 捕获所有异常，确保UI不会崩溃
                Message = "登录过程中发生错误，请稍后再试";
            }
        }

        /// <summary>
        /// 填充测试数据的方法 - 开发调试的便利功能
        ///
        /// 🎯 这个方法的作用（用遥控器例子理解）：
        /// 就像电视遥控器的"一键回到上次频道"功能：
        /// - 不用重新输入频道号
        /// - 一键就能切换到常用频道
        /// - 提高使用效率
        ///
        /// 🔧 具体功能：
        /// 1. 自动填入测试用的用户名：admin
        /// 2. 自动填入测试用的密码：123456
        /// 3. 清空之前的消息提示
        /// 4. 触发属性变更通知，界面自动更新
        ///
        /// 📝 开发时的好处：
        /// - 不用每次都手动输入测试数据
        /// - 避免输入错误
        /// - 提高开发和测试效率
        /// - 方便演示功能给别人看
        /// </summary>
        private void FillTestData()
        {
            // 填入测试用户名
            UserName = "admin";

            // 填入测试密码
            Password = "123456";

            // 清空消息提示
            Message = "已填入测试数据，可以点击登录按钮进行测试";

            // 注意：由于UserName和Password属性都有PropertyChanged通知
            // 界面上的文本框会自动显示这些值，不需要手动刷新界面
        }

        /// <summary>
        /// 执行退出登录的方法 - 带确认提示的温馨功能
        ///
        /// 🎯 为什么要确认提示？
        /// 1. 防止用户误操作
        /// 2. 提升用户体验
        /// 3. 给用户反悔的机会
        /// 4. 符合软件设计的最佳实践
        ///
        /// 🔧 实现思路：
        /// 1. 弹出确认对话框
        /// 2. 用户点击"是"才真正退出
        /// 3. 用户点击"否"或关闭对话框则取消操作
        /// </summary>
        private void ExecuteLogout()
        {
            // 显示确认对话框
            // MessageBoxResult可以获取用户点击了哪个按钮
            var result = MessageBox.Show(
                "确定要退出登录吗？\n\n退出后将返回到登录页面，当前操作将被保存。", // 消息内容
                "退出确认",                                                    // 标题
                MessageBoxButton.YesNo,                                      // 按钮类型：是/否
                MessageBoxImage.Question                                     // 图标类型：问号
            );

            // 判断用户的选择
            if (result == MessageBoxResult.Yes)
            {
                // 用户确认退出，执行退出操作
                IsLoggedIn = false;
                CurrentPage = "首页";  // 重置页面状态

                // 清空敏感信息（可选）
                UserName = "";
                Password = "";
                Message = "已安全退出登录";
            }
            // 如果用户选择"否"或关闭对话框，什么都不做，继续停留在当前页面
        }

        /// <summary>
        /// 初始化测试数据 - 模拟从后台API获取的用户数据
        ///
        /// 🎯 为什么需要测试数据？
        /// 1. 在没有真实API的情况下测试界面
        /// 2. 验证数据绑定是否正常工作
        /// 3. 方便开发和调试
        /// 4. 展示界面效果
        /// </summary>
        private void InitializeTestData()
        {
            // 模拟后台返回的用户数据
            Users.Add(new User
            {
                Id = "4f05ee4c-82dd-d271-3aa1-3a1afb62f7df",
                UserName = "123",
                UserEmail = "<EMAIL>",
                UserPhone = "18838817678",
                UserSex = true, // 男
                RoleName = new List<string> { "呼吸内科主任", "急诊科医生", "人事主管", "用户" }
            });

            Users.Add(new User
            {
                Id = "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                UserName = "admin",
                UserEmail = "<EMAIL>",
                UserPhone = "13800138000",
                UserSex = false, // 女
                RoleName = new List<string> { "系统管理员" }
            });

            Users.Add(new User
            {
                Id = "b2c3d4e5-f6g7-8901-2345-678901bcdefg",
                UserName = "doctor_wang",
                UserEmail = "<EMAIL>",
                UserPhone = "13900139000",
                UserSex = true, // 男
                RoleName = new List<string> { "心内科医生", "主治医师" }
            });

            Users.Add(new User
            {
                Id = "c3d4e5f6-g7h8-9012-3456-789012cdefgh",
                UserName = "nurse_li",
                UserEmail = "<EMAIL>",
                UserPhone = "13700137000",
                UserSex = false, // 女
                RoleName = new List<string> { "护士长", "ICU护士" }
            });

            Users.Add(new User
            {
                Id = "d4e5f6g7-h8i9-0123-4567-890123defghi",
                UserName = "tech_zhang",
                UserEmail = "<EMAIL>",
                UserPhone = "13600136000",
                UserSex = null, // 测试性别为空的情况
                RoleName = new List<string> { "技术员", "设备维护" }
            });
        }

        /// <summary>
        /// 从API加载用户列表
        ///
        /// 🎯 为什么需要这个方法？
        /// 1. 从真实后台API获取用户数据
        /// 2. 替换测试数据
        /// 3. 支持刷新功能
        /// 4. 处理API调用异常
        /// </summary>
        private async void LoadUsersFromApi()
        {
            try
            {
                // 显示加载状态
                Message = "正在加载用户列表...";

                // 使用静默API调用方式，不会抛出异常
                var users = await _userService.GetUsersSilentlyAsync();

                // 清空现有数据
                Users.Clear();

                // 如果API调用成功并返回数据
                if (users.Count > 0)
                {
                    // 添加从API获取的用户数据
                    foreach (var user in users)
                    {
                        Users.Add(user);
                    }
                    Message = $"成功加载 {users.Count} 个用户";
                }
                else
                {
                    // API调用失败或无数据，显示测试数据
                    InitializeTestData();
                    Message = "无法连接到服务器，已显示测试数据";
                }
            }
            catch (Exception)
            {
                // 以防万一捕获所有异常，确保UI不会崩溃
                // 显示测试数据作为备选
                if (Users.Count == 0)
                {
                    InitializeTestData();
                    Message = "加载用户列表失败 (已显示测试数据)";
                }
            }
        }

        /// <summary>
        /// 执行登录的异步方法 - 这是核心的HTTP请求部分
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        private async Task<bool> DoLoginAsync(string username, string password)
        {
            // 创建HttpClient - 用于发送HTTP请求
            using var httpClient = new HttpClient();
            // 设置超时时间为5秒
            httpClient.Timeout = TimeSpan.FromSeconds(5);

            try
            {
                // 第一步：准备要发送的数据（匹配你的API格式）
                var loginData = new
                {
                    userName = username,    // 用户名
                    userPwd = password      // 密码
                };

                // 第二步：将数据转换为JSON格式
                string jsonString = JsonSerializer.Serialize(loginData);

                // 第三步：创建HTTP请求内容
                var content = new StringContent(
                    jsonString,                    // JSON字符串
                    Encoding.UTF8,                 // 编码格式
                    "application/json"             // 内容类型
                );

                // 第四步：发送POST请求到你的API
                var response = await httpClient.PostAsync("http://8.140.51.34:44394/api/app/user-login-async/login", content);

                // 第五步：检查响应状态
                if (response.IsSuccessStatusCode)
                {
                    // 登录成功，读取返回的内容
                    string responseContent = await response.Content.ReadAsStringAsync();

                    // 显示简单的登录成功消息
                    MessageBox.Show("登录成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 这里可以解析返回的Token等信息
                    // 例如：var result = JsonSerializer.Deserialize<LoginResult>(responseContent);

                    return true;  // 返回成功
                }
                else
                {
                    // 登录失败，但不显示弹窗
                    // 只是静默返回失败
                    Message = "登录失败，请检查用户名和密码或网络连接";
                    return false; // 返回失败
                }
            }
            catch (HttpRequestException)
            {
                // 网络请求异常（比如服务器无法连接）
                Message = "网络连接失败，请检查网络或服务器状态";
                return false;
            }
            catch (TaskCanceledException)
            {
                // 请求超时
                Message = "请求超时，请检查网络连接";
                return false;
            }
            catch (Exception)
            {
                // 其他异常
                Message = "登录过程中发生错误";
                return false;
            }
        }




    }
}