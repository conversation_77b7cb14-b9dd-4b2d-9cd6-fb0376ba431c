using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.Common;
using WPF_MVVM_Test.MVVM_Model.ProductionOrder;
using WPF_MVVM_Test.MVVM_Services;

namespace WPF_MVVM_Test.MVVM_ViewModel.ProductionOrder
{
    /// <summary>
    /// 增强版生产工单ViewModel - 按照项目代码风格实现
    /// 
    /// 🎯 这个类的作用：
    /// 1. 管理生产工单页面的所有数据和业务逻辑
    /// 2. 提供搜索、分页、状态筛选等功能
    /// 3. 处理工单的各种操作（开始、取消、结束、撤回等）
    /// 4. 遵循项目的MVVM模式和代码风格
    /// </summary>
    public class EnhancedProductionOrderViewModel : BaseViewModel
    {
        #region 私有字段

        private readonly MockProductionOrderService _productionOrderService;
        private bool _isLoading;
        private string _message = "";

        // 分页相关字段
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalCount = 0;
        private int _totalPages = 0;
        private string _goToPage = "1";

        // 搜索相关字段
        private string _searchOrderNumber = "";
        private string _searchTaskName = "";
        private string _searchStatus = "";

        #endregion

        #region 构造函数

        public EnhancedProductionOrderViewModel()
        {
            _productionOrderService = new MockProductionOrderService();

            // 初始化命令
            InitializeCommands();

            // 初始化数据
            InitializeData();

            // 加载数据
            _ = LoadProductionOrdersAsync();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 生产工单列表
        /// </summary>
        public ObservableCollection<ProductionOrderModel> ProductionOrders { get; set; } = new ObservableCollection<ProductionOrderModel>();

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 消息提示
        /// </summary>
        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        #endregion

        #region 分页属性

        public int CurrentPage
        {
            get => _currentPage;
            set => SetProperty(ref _currentPage, value);
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                if (SetProperty(ref _pageSize, value))
                {
                    CurrentPage = 1; // 重置到第一页
                    _ = LoadProductionOrdersAsync();
                }
            }
        }

        public int TotalCount
        {
            get => _totalCount;
            set => SetProperty(ref _totalCount, value);
        }

        public int TotalPages
        {
            get => _totalPages;
            set => SetProperty(ref _totalPages, value);
        }

        public string GoToPage
        {
            get => _goToPage;
            set => SetProperty(ref _goToPage, value);
        }

        public string PageInfo => $"共{TotalCount}条";

        #endregion

        #region 搜索属性

        public string SearchOrderNumber
        {
            get => _searchOrderNumber;
            set => SetProperty(ref _searchOrderNumber, value);
        }

        public string SearchTaskName
        {
            get => _searchTaskName;
            set => SetProperty(ref _searchTaskName, value);
        }

        public string SearchStatus
        {
            get => _searchStatus;
            set => SetProperty(ref _searchStatus, value);
        }

        #endregion

        #region 选项列表

        public List<int> PageSizeOptions { get; } = new List<int> { 5, 10, 20, 50, 100 };

        public List<StatusOption> StatusOptions { get; } = new List<StatusOption>
        {
            new StatusOption { Text = "全部", Value = "" },
            new StatusOption { Text = "未派工", Value = "未派工" },
            new StatusOption { Text = "已下达", Value = "已下达" },
            new StatusOption { Text = "进行中", Value = "进行中" },
            new StatusOption { Text = "已完成", Value = "已完成" },
            new StatusOption { Text = "已关闭", Value = "已关闭" },
            new StatusOption { Text = "已暂停", Value = "已暂停" }
        };

        #endregion

        #region 命令

        public ICommand LoadDataCommand { get; private set; }
        public ICommand PreviousPageCommand { get; private set; }
        public ICommand NextPageCommand { get; private set; }
        public ICommand GoToPageCommand { get; private set; }
        public ICommand SearchCommand { get; private set; }
        public ICommand ResetSearchCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }

        // 工单操作命令
        public ICommand StartSelectedCommand { get; private set; }
        public ICommand CancelSelectedCommand { get; private set; }
        public ICommand EndSelectedCommand { get; private set; }
        public ICommand RecallSelectedCommand { get; private set; }
        public ICommand ScheduleProductionCommand { get; private set; }
        public ICommand ViewProductionCommand { get; private set; }

        #endregion

        #region 初始化方法

        private void InitializeCommands()
        {
            // 数据加载命令
            LoadDataCommand = CreateCommand(async () => await LoadProductionOrdersAsync());
            RefreshCommand = CreateCommand(async () => await LoadProductionOrdersAsync());

            // 分页命令
            PreviousPageCommand = CreateCommand(PreviousPage, CanPreviousPage);
            NextPageCommand = CreateCommand(NextPage, CanNextPage);
            GoToPageCommand = CreateCommand(GoToSpecificPage);

            // 搜索命令
            SearchCommand = CreateCommand(async () => await SearchProductionOrdersAsync());
            ResetSearchCommand = CreateCommand(ResetSearch);

            // 工单操作命令
            StartSelectedCommand = CreateCommand(async () => await StartSelectedProduction());
            CancelSelectedCommand = CreateCommand(async () => await CancelSelectedProduction());
            EndSelectedCommand = CreateCommand(async () => await EndSelectedProduction());
            RecallSelectedCommand = CreateCommand(async () => await RecallSelectedProduction());

            ScheduleProductionCommand = CreateCommand<ProductionOrderModel>(async (order) => await ScheduleProduction(order));
            ViewProductionCommand = CreateCommand<ProductionOrderModel>(async (order) => await ViewProduction(order));
        }

        private void InitializeData()
        {
            // 初始化分页信息
            CurrentPage = 1;
            PageSize = 10;
            TotalCount = 0;
            TotalPages = 0;
            Message = "正在加载生产工单数据...";
        }

        #endregion

        #region 数据加载方法

        /// <summary>
        /// 加载生产工单数据
        /// </summary>
        private async Task LoadProductionOrdersAsync()
        {
            try
            {
                IsLoading = true;
                Message = "正在加载数据...";

                var response = await _productionOrderService.GetProductionOrderListAsync(
                    SearchOrderNumber,
                    SearchTaskName,
                    SearchStatus,
                    CurrentPage,
                    PageSize);

                if (response.IsSuc && response.Data != null)
                {
                    // 清空现有数据
                    ProductionOrders.Clear();

                    // 添加新数据
                    foreach (var order in response.Data.Data)
                    {
                        ProductionOrders.Add(order);
                    }

                    // 更新分页信息
                    TotalCount = response.Data.TotalCount;
                    TotalPages = response.Data.TotalPage;

                    Message = $"加载完成，共{TotalCount}条记录";
                }
                else
                {
                    Message = $"加载失败: {response.Msg}";
                }
            }
            catch (Exception ex)
            {
                Message = $"加载数据时发生错误: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 搜索生产工单
        /// </summary>
        private async Task SearchProductionOrdersAsync()
        {
            CurrentPage = 1; // 重置到第一页
            await LoadProductionOrdersAsync();
        }

        #endregion

        #region 分页方法

        private void PreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                _ = LoadProductionOrdersAsync();
            }
        }

        private bool CanPreviousPage()
        {
            return CurrentPage > 1;
        }

        private void NextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                _ = LoadProductionOrdersAsync();
            }
        }

        private bool CanNextPage()
        {
            return CurrentPage < TotalPages;
        }

        private void GoToSpecificPage()
        {
            if (int.TryParse(GoToPage, out int page) && page >= 1 && page <= TotalPages)
            {
                CurrentPage = page;
                _ = LoadProductionOrdersAsync();
            }
            else
            {
                MessageBox.Show($"请输入有效的页码 (1-{TotalPages})", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                GoToPage = CurrentPage.ToString();
            }
        }

        #endregion

        #region 搜索方法

        private void ResetSearch()
        {
            SearchOrderNumber = "";
            SearchTaskName = "";
            SearchStatus = "";
            CurrentPage = 1;
            _ = LoadProductionOrdersAsync();
        }

        #endregion

        #region 工单操作方法

        private async Task StartSelectedProduction()
        {
            var selectedOrders = ProductionOrders.Where(o => o.IsSelected).ToList();
            if (!selectedOrders.Any())
            {
                MessageBox.Show("请选择要开始的生产工单", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                foreach (var order in selectedOrders)
                {
                    var response = await _productionOrderService.StartProductionOrderAsync(order.Id);
                    if (!response.IsSuc)
                    {
                        MessageBox.Show($"开始工单 {order.OrderNumber} 失败: {response.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }

                // 重新加载数据
                await LoadProductionOrdersAsync();
                MessageBox.Show("操作完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CancelSelectedProduction()
        {
            var selectedOrders = ProductionOrders.Where(o => o.IsSelected).ToList();
            if (!selectedOrders.Any())
            {
                MessageBox.Show("请选择要取消的生产工单", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("确定要取消选中的生产工单吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    foreach (var order in selectedOrders)
                    {
                        var response = await _productionOrderService.CancelProductionOrderAsync(order.Id);
                        if (!response.IsSuc)
                        {
                            MessageBox.Show($"取消工单 {order.OrderNumber} 失败: {response.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }

                    await LoadProductionOrdersAsync();
                    MessageBox.Show("操作完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task EndSelectedProduction()
        {
            var selectedOrders = ProductionOrders.Where(o => o.IsSelected).ToList();
            if (!selectedOrders.Any())
            {
                MessageBox.Show("请选择要结束的生产工单", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("确定要结束选中的生产工单吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    foreach (var order in selectedOrders)
                    {
                        var response = await _productionOrderService.EndProductionOrderAsync(order.Id);
                        if (!response.IsSuc)
                        {
                            MessageBox.Show($"结束工单 {order.OrderNumber} 失败: {response.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }

                    await LoadProductionOrdersAsync();
                    MessageBox.Show("操作完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task RecallSelectedProduction()
        {
            var selectedOrders = ProductionOrders.Where(o => o.IsSelected).ToList();
            if (!selectedOrders.Any())
            {
                MessageBox.Show("请选择要撤回的生产工单", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("确定要撤回选中的生产工单吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    foreach (var order in selectedOrders)
                    {
                        var response = await _productionOrderService.RecallProductionOrderAsync(order.Id);
                        if (!response.IsSuc)
                        {
                            MessageBox.Show($"撤回工单 {order.OrderNumber} 失败: {response.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }

                    await LoadProductionOrdersAsync();
                    MessageBox.Show("操作完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task ScheduleProduction(ProductionOrderModel order)
        {
            if (order == null) return;

            MessageBox.Show($"正在为工单 {order.OrderNumber} 进行排产...", "排产", MessageBoxButton.OK, MessageBoxImage.Information);
            // 这里可以打开排产对话框
        }

        private async Task ViewProduction(ProductionOrderModel order)
        {
            if (order == null) return;

            MessageBox.Show($"正在查看工单 {order.OrderNumber} 的详细信息...", "查看", MessageBoxButton.OK, MessageBoxImage.Information);
            // 这里可以打开详情窗口
        }

        #endregion

        #region 资源释放

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _productionOrderService?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    /// <summary>
    /// 状态选项模型
    /// </summary>
    public class StatusOption
    {
        public string Text { get; set; } = "";
        public string Value { get; set; } = "";
    }
}
