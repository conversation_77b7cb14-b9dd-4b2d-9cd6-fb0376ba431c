using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.WorkOrderTask;

namespace WPF_MVVM_Test.MVVM_ViewModel.WorkOrderTask
{
    /// <summary>
    /// 工单任务编辑/派工弹窗ViewModel
    /// </summary>
    public class WorkOrderTaskEditViewModel : INotifyPropertyChanged
    {
        #region 私有字段
        private WorkOrderTaskModel _currentTask;
        private bool _isEditMode;
        private string _dialogTitle;
        private string _selectedWorkGroup;
        private string _selectedAssignee;
        private string _selectedQualityDepartment;
        private string _selectedQualityPersonnel;
        private string _notes;
        private string _otherMembersText;
        private List<string> _selectedOtherMembers;
        private ObservableCollection<string> _workGroups;
        private ObservableCollection<string> _assignees;
        private ObservableCollection<string> _qualityDepartments;
        private ObservableCollection<string> _qualityPersonnels;
        #endregion

        #region 公共属性
        public WorkOrderTaskModel CurrentTask
        {
            get => _currentTask;
            set
            {
                _currentTask = value;
                OnPropertyChanged();
                LoadTaskData();
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged();
                UpdateDialogTitle();
            }
        }

        public string DialogTitle
        {
            get => _dialogTitle;
            set
            {
                _dialogTitle = value;
                OnPropertyChanged();
            }
        }

        public string SelectedWorkGroup
        {
            get => _selectedWorkGroup;
            set
            {
                _selectedWorkGroup = value;
                OnPropertyChanged();
                LoadAssignees();
            }
        }

        public string SelectedAssignee
        {
            get => _selectedAssignee;
            set
            {
                _selectedAssignee = value;
                OnPropertyChanged();
            }
        }

        public string SelectedQualityDepartment
        {
            get => _selectedQualityDepartment;
            set
            {
                _selectedQualityDepartment = value;
                OnPropertyChanged();
                LoadQualityPersonnels();
            }
        }

        public string SelectedQualityPersonnel
        {
            get => _selectedQualityPersonnel;
            set
            {
                _selectedQualityPersonnel = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public string OtherMembersText
        {
            get => _otherMembersText;
            set
            {
                _otherMembersText = value;
                OnPropertyChanged();
            }
        }

        public List<string> SelectedOtherMembers
        {
            get => _selectedOtherMembers;
            set
            {
                _selectedOtherMembers = value;
                UpdateOtherMembersText();
            }
        }

        public ObservableCollection<string> WorkGroups
        {
            get => _workGroups;
            set
            {
                _workGroups = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> Assignees
        {
            get => _assignees;
            set
            {
                _assignees = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> QualityDepartments
        {
            get => _qualityDepartments;
            set
            {
                _qualityDepartments = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> QualityPersonnels
        {
            get => _qualityPersonnels;
            set
            {
                _qualityPersonnels = value;
                OnPropertyChanged();
            }
        }
        #endregion

        #region 命令
        public ICommand ConfirmCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand SelectOtherMembersCommand { get; private set; }
        #endregion

        #region 事件
        public event Action<bool> RequestClose;
        public event PropertyChangedEventHandler PropertyChanged;
        #endregion

        #region 构造函数
        public WorkOrderTaskEditViewModel()
        {
            InitializeData();
            InitializeCommands();
        }

        public WorkOrderTaskEditViewModel(WorkOrderTaskModel task, bool isEditMode = false)
        {
            CurrentTask = task;
            IsEditMode = isEditMode;
            InitializeData();
            InitializeCommands();
        }
        #endregion

        #region 私有方法
        private void InitializeData()
        {
            // 初始化下拉选项数据
            WorkGroups = new ObservableCollection<string>
            {
                "请选择班组",
                "生产一班",
                "生产二班",
                "生产三班",
                "质检班组",
                "包装班组"
            };

            QualityDepartments = new ObservableCollection<string>
            {
                "请选择质检部门",
                "质检一部",
                "质检二部",
                "质检三部",
                "进料检验部",
                "成品检验部"
            };

            QualityPersonnels = new ObservableCollection<string>();

            Assignees = new ObservableCollection<string>();

            // 设置默认值
            SelectedWorkGroup = "请选择班组";
            SelectedQualityDepartment = "请选择质检部门";
            SelectedQualityPersonnel = "请选择质检人员";
            SelectedOtherMembers = new List<string>();
            OtherMembersText = "请选择其他成员";
        }

        private void InitializeCommands()
        {
            ConfirmCommand = new RelayCommand(async () => await ConfirmAsync(), CanConfirm);
            CancelCommand = new RelayCommand(() => RequestClose?.Invoke(false));
            SelectOtherMembersCommand = new RelayCommand(SelectOtherMembers);
        }

        private void UpdateDialogTitle()
        {
            DialogTitle = IsEditMode ? "编辑工单" : "任务派工";
        }

        private void LoadTaskData()
        {
            if (CurrentTask != null)
            {
                // 加载任务数据到界面
                Notes = CurrentTask.Remarks ?? "";
                // 这里可以根据任务数据设置其他字段
            }
            UpdateDialogTitle();
        }

        private void LoadAssignees()
        {
            if (SelectedWorkGroup == "请选择班组")
            {
                Assignees.Clear();
                Assignees.Add("请选择任务负责人");
                SelectedAssignee = "请选择任务负责人";
                return;
            }

            // 根据选择的班组加载对应的人员
            Assignees.Clear();
            Assignees.Add("请选择任务负责人");

            switch (SelectedWorkGroup)
            {
                case "生产一班":
                    Assignees.Add("张班长");
                    Assignees.Add("李师傅");
                    Assignees.Add("王师傅");
                    break;
                case "生产二班":
                    Assignees.Add("赵班长");
                    Assignees.Add("钱师傅");
                    Assignees.Add("孙师傅");
                    break;
                case "生产三班":
                    Assignees.Add("周班长");
                    Assignees.Add("吴师傅");
                    Assignees.Add("郑师傅");
                    break;
                case "质检班组":
                    Assignees.Add("质检组长");
                    Assignees.Add("质检员A");
                    Assignees.Add("质检员B");
                    break;
                case "包装班组":
                    Assignees.Add("包装组长");
                    Assignees.Add("包装员A");
                    Assignees.Add("包装员B");
                    break;
            }

            SelectedAssignee = "请选择任务负责人";
        }

        private void LoadQualityPersonnels()
        {
            if (SelectedQualityDepartment == "请选择质检部门")
            {
                QualityPersonnels.Clear();
                QualityPersonnels.Add("请选择质检人员");
                SelectedQualityPersonnel = "请选择质检人员";
                return;
            }

            // 根据选择的质检部门加载对应的质检人员
            QualityPersonnels.Clear();
            QualityPersonnels.Add("请选择质检人员");

            switch (SelectedQualityDepartment)
            {
                case "质检一部":
                    QualityPersonnels.Add("张质检");
                    QualityPersonnels.Add("李质检");
                    QualityPersonnels.Add("王质检");
                    break;
                case "质检二部":
                    QualityPersonnels.Add("赵质检");
                    QualityPersonnels.Add("钱质检");
                    QualityPersonnels.Add("孙质检");
                    break;
                case "质检三部":
                    QualityPersonnels.Add("周质检");
                    QualityPersonnels.Add("吴质检");
                    QualityPersonnels.Add("郑质检");
                    break;
                case "进料检验部":
                    QualityPersonnels.Add("进料检验员A");
                    QualityPersonnels.Add("进料检验员B");
                    break;
                case "成品检验部":
                    QualityPersonnels.Add("成品检验员A");
                    QualityPersonnels.Add("成品检验员B");
                    break;
            }

            SelectedQualityPersonnel = "请选择质检人员";
        }

        private void SelectOtherMembers()
        {
            try
            {
                var dialog = new MVVM_View.WorkOrderTask.EmployeeSelectionDialog();
                var result = dialog.ShowDialog();

                if (result == true && dialog.IsConfirmed)
                {
                    SelectedOtherMembers = dialog.SelectedEmployeeNames ?? new List<string>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开人员选择对话框失败: {ex.Message}");
            }
        }

        private void UpdateOtherMembersText()
        {
            if (SelectedOtherMembers == null || SelectedOtherMembers.Count == 0)
            {
                OtherMembersText = "请选择其他成员";
            }
            else
            {
                OtherMembersText = string.Join(", ", SelectedOtherMembers);
            }
        }

        private bool CanConfirm()
        {
            return !string.IsNullOrEmpty(SelectedWorkGroup) &&
                   SelectedWorkGroup != "请选择班组" &&
                   !string.IsNullOrEmpty(SelectedAssignee) &&
                   SelectedAssignee != "请选择任务负责人";
        }

        private async Task ConfirmAsync()
        {
            try
            {
                // 这里执行保存逻辑
                if (CurrentTask != null)
                {
                    // 更新任务信息
                    CurrentTask.Remarks = Notes;
                    // 这里可以调用服务保存数据
                    await Task.Delay(100); // 模拟异步操作

                    if (!IsEditMode)
                    {
                        // 派工操作，更新状态
                        CurrentTask.Status = "已下达";
                    }
                }

                RequestClose?.Invoke(true);
            }
            catch (Exception ex)
            {
                // 错误处理
                System.Diagnostics.Debug.WriteLine($"保存失败: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
