<Window x:Class="WPF_MVVM_Test.MVVM_View.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View"
        xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel"
        xmlns:uc="clr-namespace:WPF_MVVM_Test.MVVM_View.UserControl"
        xmlns:pl="clr-namespace:WPF_MVVM_Test.MVVM_View.Productplan"
        xmlns:po="clr-namespace:WPF_MVVM_Test.MVVM_View.ProductionOrder"
        xmlns:bom="clr-namespace:WPF_MVVM_Test.MVVM_View.Bom"
        xmlns:material="clr-namespace:WPF_MVVM_Test.MVVM_View.Material"
        xmlns:process="clr-namespace:WPF_MVVM_Test.MVVM_View.Process_process"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="MES管理系统"
        Height="700"
        Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <!-- 设置DataContext -->
    <Window.DataContext>
        <vm:MainWindowViewModel/>
    </Window.DataContext>

    <!-- 添加转换器资源 -->
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <material:RowIndexConverter x:Key="RowIndexConverter"/>

        <!-- ViewModel到UserControl的映射 -->
        <DataTemplate DataType="{x:Type vm:ReportManagementViewModel}">
            <uc:ReportManagement/>
        </DataTemplate>
    </Window.Resources>

    <Grid>
        <!-- 登录页面 - 当IsLoggedIn为false时显示 -->
        <Grid Visibility="{Binding IsLoggedIn, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
            <materialDesign:Card
                Width="380"
                Height="500"
                Margin="20"
                VerticalAlignment="Center"
                HorizontalAlignment="Center">

                <!-- 卡片内容区域 -->
                <StackPanel Margin="30">
                    <!-- 标题 -->
                    <TextBlock
                        Text="用户登录"
                        FontSize="24"
                        FontWeight="Bold"
                        HorizontalAlignment="Center"
                        Margin="0,0,0,30"/>

                    <!-- 用户名区域 -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock
                            Text="账号"
                            FontSize="14"
                            Margin="0,0,0,5"
                            Foreground="#666"/>
                        <TextBox
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            materialDesign:HintAssist.Hint="请输入用户名"
                            Text="{Binding UserName, UpdateSourceTrigger=PropertyChanged}"
                            FontSize="16"
                            Height="50"/>
                    </StackPanel>

                    <!-- 密码区域 -->
                    <StackPanel Margin="0,0,0,30">
                        <TextBlock
                            Text="密码"
                            FontSize="14"
                            Margin="0,0,0,5"
                            Foreground="#666"/>
                        <PasswordBox x:Name="PasswordBox"
                                     Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                     materialDesign:HintAssist.Hint="请输入密码"
                                     FontSize="16"
                                     Height="50"/>
                    </StackPanel>

                    <!-- 填充测试数据按钮 -->
                    <Button Command="{Binding FillTestDataCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="填充测试数据"
                            Height="40"
                            FontSize="14"
                            Margin="0,0,0,10"
                            Cursor="Hand"/>

                    <!-- 登录按钮 -->
                    <Button Command="{Binding LoginCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="登录"
                            Height="40"
                            FontSize="16"
                            Foreground="White"
                            Cursor="Hand"/>

                    <!-- 消息显示区域 -->
                    <TextBlock Text="{Binding Message}"
                               FontSize="14"
                               Margin="0,20,0,0"
                               HorizontalAlignment="Center"
                               Foreground="Red"
                               TextWrapping="Wrap"/>
                    <!-- 移除了有问题的Visibility转换器 -->
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- 主页面 - 当IsLoggedIn为true时显示 -->
        <Grid Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <!-- 左侧菜单 -->
                <ColumnDefinition Width="*"/>
                <!-- 右侧内容 -->
            </Grid.ColumnDefinitions>

            <!-- 左侧导航菜单 -->
            <Border Grid.Column="0"
                    Background="#2E3440">
                <StackPanel Margin="0,20,0,0">
                    <!-- 系统标题 -->
                    <TextBlock Text="管理系统"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="White"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,30"/>

                    <!-- 菜单按钮 -->
                    <Button Command="{Binding ShowHomeCommand}"
                            Content="🏠 首页"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>

                    <Button Command="{Binding ShowUserCommand}"
                            Content="👥 用户管理"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>

                    <Button Command="{Binding ShowReportCommand}"
                            Content="📊 报表管理"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>
                    <Button Command="{Binding ShowProductPlanCommand}"
                            Content="📋 生产计划"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>
                    <Button Command="{Binding ShowProductOrderCommand}"
                            Content="🏭 生产工单"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>
                    <Button Command="{Binding ShowBomCommand}"
                            Content="📄 Bom管理"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>

                    <Button Command="{Binding ShowMaterialCommand}"
                            Content="📦 物料管理"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>

                    <Button Command="{Binding ShowProcessRouteCommand}"
                            Content="⚙️ 工艺路线管理"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>
                    
                    <Button Command="{Binding ShowAiChatCommand}"
                            Content="🤖 AI助手"
                            Height="50"
                            FontSize="14"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Margin="10,5"
                            Cursor="Hand"/>

                        <Button Command="{Binding ShowDataExportCommand}"
                                Content="📊 数据导出"
                                Height="50"
                                FontSize="14"
                                Foreground="White"
                                Background="Transparent"
                                BorderThickness="0"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Margin="10,5"
                                Cursor="Hand"/>

                        <Button Command="{Binding ShowSettingsCommand}"
                                Content="⚙️ 系统设置"
                                Height="50"
                                FontSize="14"
                                Foreground="White"
                                Background="Transparent"
                                BorderThickness="0"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Margin="10,5"
                                Cursor="Hand" />

                        <!-- 底部退出按钮 -->
                        <Button Command="{Binding LogoutCommand}"
                                Content="🚪 退出登录"
                                Height="40"
                                FontSize="14"
                                Foreground="#BF616A"
                                Background="Transparent"
                                BorderBrush="#BF616A"
                                BorderThickness="1"
                                HorizontalAlignment="Stretch"
                                Margin="10,50,10,10"
                                Cursor="Hand"/>
                </StackPanel>
            </Border>

            <!-- 右侧内容区域 -->
            <Grid Grid.Column="1"
                  Background="#F5F5F5">
                <!-- 顶部标题栏 -->
                <Border Height="60"
                        VerticalAlignment="Top"
                        Background="White">
                    <StackPanel Orientation="Horizontal"
                                VerticalAlignment="Center"
                                Margin="20,0">
                        <TextBlock Text="{Binding CurrentPage}"
                                   FontSize="20"
                                   FontWeight="Bold"/>
                        <TextBlock Text=" - 欢迎使用管理系统"
                                   FontSize="14"
                                   VerticalAlignment="Bottom"
                                   Margin="10,0,0,2"/>
                    </StackPanel>
                </Border>

                <!-- 内容区域 - 使用ContentControl动态加载页面 -->
                <ScrollViewer Margin="0,60,0,0"
                              VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">

                        <!-- 首页内容 - 使用UserControl -->
                        <ContentControl Visibility="{Binding IsHomePageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:HomePage/>
                        </ContentControl>

                        <!-- 用户管理页面 - 使用UserControl -->
                        <ContentControl Visibility="{Binding IsUserPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:UserManagement DataContext="{Binding}"/>
                        </ContentControl>

                        <!-- 报表管理页面 - 使用UserControl -->
                        <ContentControl Visibility="{Binding IsReportPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:ReportManagement DataContext="{Binding ReportManagementViewModel}"/>
                        </ContentControl>


                        <!-- 生产计划页面 - 修正命名空间引用 -->
                        <ContentControl Visibility="{Binding IsProductPlanPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <pl:ProductplanList/>
                        </ContentControl>
                        <!-- Bom管理页面 - 使用UserControl -->
                        <ContentControl Visibility="{Binding IsBomPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:BomManagement/>
                        </ContentControl>

                        <!-- BOM新增页面 -->
                        <ContentControl Visibility="{Binding IsBomAddPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <bom:BomAddPage/>
                        </ContentControl>

                        <!-- 新增生产计划页面 -->
                        <ContentControl Visibility="{Binding IsAddProductPlanPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <pl:AddProductPlan DataContext="{Binding AddProductPlanViewModel}"/>
                        </ContentControl>

                        <!-- 生产工单页面 -->
                        <ContentControl Visibility="{Binding IsProductOrderPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <po:ProductionOrderControl DataContext="{Binding ProductionOrderViewModel}"/>
                        </ContentControl>

                        <!-- 物料管理页面 -->
                        <ContentControl Visibility="{Binding IsMaterialPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <material:MaterialManagement/>
                        </ContentControl>

                        <!-- 工艺路线管理页面 -->
                        <ContentControl Visibility="{Binding IsProcessRoutePageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <process:ProcessRouteManagement/>
                        </ContentControl>

                        <!-- AI聊天页面 -->
                        <ContentControl Visibility="{Binding IsAiChatPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:AiChat DataContext="{Binding ChatViewModel}"/>
                        </ContentControl>

                        <!-- 数据导出页面 -->
                        <ContentControl Visibility="{Binding IsDataExportPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:DataExport DataContext="{Binding ExportViewModel}"/>
                        </ContentControl>

                        <!-- 系统设置页面 - 使用UserControl -->
                        <ContentControl Visibility="{Binding IsSettingsPageVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <uc:SystemSettings/>
                        </ContentControl>







                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Grid>
</Window>
