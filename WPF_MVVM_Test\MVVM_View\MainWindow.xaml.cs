using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_ViewModel;
using System.Globalization;
using System.Windows.Data;
using System;

namespace WPF_MVVM_Test.MVVM_View
{
    /// <summary>
    /// MainWindow的代码后置文件 - MVVM模式中View层的代码部分
    /// 
    /// 设计原则：
    /// 1. 尽量减少代码后置文件中的逻辑
    /// 2. 只处理无法通过数据绑定解决的UI问题
    /// 3. 不包含业务逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private MainWindowViewModel? _viewModel;

        public MainWindow()
        {
            InitializeComponent();

            // 设置DataContext
            _viewModel = new MainWindowViewModel();
            this.DataContext = _viewModel;

            // 只保留必要的PasswordBox密码同步
            PasswordBox.PasswordChanged += PasswordBox_PasswordChanged;

            // 监听ViewModel的属性变化，实现Password的双向同步
            if (_viewModel != null)
            {
                _viewModel.PropertyChanged += ViewModel_PropertyChanged;
            }
        }

        /// <summary>
        /// PasswordBox密码变更事件处理 - View -> ViewModel 同步
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_viewModel != null)
            {
                _viewModel.Password = PasswordBox.Password;
            }
        }

        /// <summary>
        /// ViewModel属性变化事件处理 - ViewModel -> View 同步（仅处理Password）
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            // 只处理Password属性的反向同步（如填充测试数据时）
            if (e.PropertyName == nameof(MainWindowViewModel.Password) && _viewModel != null)
            {
                // 避免循环更新：只有当PasswordBox的值与ViewModel不同时才更新
                if (PasswordBox.Password != _viewModel.Password)
                {
                    PasswordBox.Password = _viewModel.Password;
                }
            }
        }
    }

    /// <summary>
    /// 反向布尔值到可见性转换器
    /// true -> Hidden, false -> Visible
    /// </summary>
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Hidden : Visibility.Visible;
            }
            return Visibility.Hidden;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility != Visibility.Visible;
            }
            return true;
        }
    }

    /// <summary>
    /// 反向布尔值转换器
    /// true -> false, false -> true
    /// </summary>
    public class InverseBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }
}
