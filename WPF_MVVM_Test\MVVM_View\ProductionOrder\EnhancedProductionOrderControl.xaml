<UserControl x:Class="WPF_MVVM_Test.MVVM_View.ProductionOrder.EnhancedProductionOrderControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.ProductionOrder"
             mc:Ignorable="d"
             d:DesignHeight="800"
        d:DesignWidth="1400"
             Background="#F5F5F5">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle"
                TargetType="{x:Type Button}">
            <Setter Property="Background"
                    Value="#1890FF"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="12,6"/>
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#40A9FF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle"
                TargetType="{x:Type Button}"
                BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background"
                    Value="#52C41A"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#73D13D"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="WarningButtonStyle"
                TargetType="{x:Type Button}"
                BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background"
                    Value="#FA8C16"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#FFA940"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DangerButtonStyle"
                TargetType="{x:Type Button}"
                BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background"
                    Value="#FF4D4F"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#FF7875"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle"
                TargetType="{x:Type TextBox}">
            <Setter Property="Height"
                    Value="32"/>
            <Setter Property="Padding"
                    Value="8,6"/>
            <Setter Property="BorderBrush"
                    Value="#D9D9D9"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="VerticalContentAlignment"
                    Value="Center"/>
        </Style>

        <!-- 下拉框样式 -->
        <Style x:Key="ComboBoxStyle"
                TargetType="{x:Type ComboBox}">
            <Setter Property="Height"
                    Value="32"/>
            <Setter Property="BorderBrush"
                    Value="#D9D9D9"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="VerticalContentAlignment"
                    Value="Center"/>
        </Style>

        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 状态颜色转换器 -->
        <local:StatusColorConverter x:Key="StatusColorConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和操作按钮 -->
        <Grid Grid.Row="0"
                Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0"
                    Orientation="Horizontal">
                <TextBlock Text="生产工单"
                        FontSize="24"
                        FontWeight="Bold"
                        VerticalAlignment="Center"/>
                <TextBlock Text="{Binding Message}"
                        FontSize="12"
                        Foreground="#666"
                        VerticalAlignment="Center"
                        Margin="20,0,0,0"/>
            </StackPanel>

            <StackPanel Grid.Column="1"
                    Orientation="Horizontal">
                <Button Content="派工"
                        Style="{StaticResource SuccessButtonStyle}"
                        Margin="0,0,8,0"
                        Command="{Binding StartSelectedCommand}"/>
                <Button Content="开工"
                        Style="{StaticResource SuccessButtonStyle}"
                        Margin="0,0,8,0"
                        Command="{Binding StartSelectedCommand}"/>
                <Button Content="关闭"
                        Style="{StaticResource DangerButtonStyle}"
                        Margin="0,0,8,0"
                        Command="{Binding CancelSelectedCommand}"/>
                <Button Content="重启"
                        Style="{StaticResource WarningButtonStyle}"
                        Margin="0,0,8,0"
                        Command="{Binding RecallSelectedCommand}"/>
                <Button Content="查看"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Margin="0,0,8,0"/>
                <Button Content="复制"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Margin="0,0,8,0"/>
                <Button Content="刷新"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Margin="0,0,8,0"
                        Command="{Binding RefreshCommand}"/>
                <Button Content="导出"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding ExportCommand}"/>
            </StackPanel>
        </Grid>

        <!-- 搜索区域 -->
        <Border Grid.Row="1"
                Background="White"
                CornerRadius="6"
                Padding="16"
                Margin="0,0,0,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 第一行搜索条件 -->
                <Grid Grid.Row="0"
                        Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 请输入任务编号/名称 -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="请输入任务编号/名称"
                                FontSize="12"
                                Margin="0,0,0,6"/>
                        <TextBox Text="{Binding SearchTaskName, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 ToolTip="输入任务编号或名称进行搜索"/>
                    </StackPanel>

                    <!-- 请输入工单编号/名称 -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="请输入工单编号/名称"
                                FontSize="12"
                                Margin="0,0,0,6"/>
                        <TextBox Text="{Binding SearchOrderNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 ToolTip="输入工单编号或名称进行搜索"/>
                    </StackPanel>

                    <!-- 请选择工序 -->
                    <StackPanel Grid.Column="4">
                        <TextBlock Text="请选择工序"
                                FontSize="12"
                                Margin="0,0,0,6"/>
                        <ComboBox Style="{StaticResource ComboBoxStyle}">
                            <ComboBoxItem Content="全部工序"/>
                            <ComboBoxItem Content="工序一"/>
                            <ComboBoxItem Content="工序二"/>
                            <ComboBoxItem Content="工序三"/>
                            <ComboBoxItem Content="工序四"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- 请选择状态 -->
                    <StackPanel Grid.Column="6">
                        <TextBlock Text="请选择状态"
                                FontSize="12"
                                Margin="0,0,0,6"/>
                        <ComboBox ItemsSource="{Binding StatusOptions}"
                                  SelectedValue="{Binding SearchStatus}"
                                  DisplayMemberPath="Text"
                                  SelectedValuePath="Value"
                                  Style="{StaticResource ComboBoxStyle}"/>
                    </StackPanel>
                </Grid>

                <!-- 第二行：工单任务 标签和按钮 -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                            Text="工单任务"
                            FontSize="14"
                            FontWeight="Bold"
                            VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="2"
                            Orientation="Horizontal">
                        <Button Content="查询"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Margin="0,0,8,0"
                                Command="{Binding SearchCommand}"/>
                        <Button Content="重置"
                                Style="{StaticResource WarningButtonStyle}"
                                Command="{Binding ResetSearchCommand}"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 数据表格 -->
        <Border Grid.Row="2"
                Background="White"
                CornerRadius="6"
                Margin="0,0,0,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 数据表格 -->
                <DataGrid Grid.Row="0"
                        ItemsSource="{Binding ProductionOrders}"
                          AutoGenerateColumns="False"
                        CanUserAddRows="False"
                          CanUserDeleteRows="False"
                        CanUserReorderColumns="True"
                          CanUserResizeColumns="True"
                        CanUserResizeRows="False"
                          GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                          AlternatingRowBackground="#FAFAFA"
                        RowBackground="White"
                          BorderThickness="0"
                        HorizontalGridLinesBrush="#F0F0F0"
                          VerticalGridLinesBrush="Transparent"
                        FontSize="12"
                          RowHeight="40"
                        HeaderHeight="40">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background"
                                    Value="#F8F9FA"/>
                            <Setter Property="Foreground"
                                    Value="#262626"/>
                            <Setter Property="FontWeight"
                                    Value="Bold"/>
                            <Setter Property="FontSize"
                                    Value="12"/>
                            <Setter Property="BorderBrush"
                                    Value="#F0F0F0"/>
                            <Setter Property="BorderThickness"
                                    Value="0,0,1,1"/>
                            <Setter Property="Padding"
                                    Value="8,0"/>
                            <Setter Property="HorizontalContentAlignment"
                                    Value="Center"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness"
                                    Value="0"/>
                            <Setter Property="Padding"
                                    Value="8,0"/>
                            <Setter Property="VerticalAlignment"
                                    Value="Center"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="DataGridCell">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.Columns>
                        <!-- 复选框列 -->
                        <DataGridTemplateColumn Header=""
                                Width="40"
                                IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                              HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="序号"
                                Binding="{Binding Index}"
                                Width="60"
                                IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment"
                                            Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="任务编号"
                                Binding="{Binding TaskNumber}"
                                Width="100"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="任务名称"
                                Binding="{Binding TaskName}"
                                Width="120"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="站点名称"
                                Binding="{Binding StationName}"
                                Width="80"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="工单编号"
                                Binding="{Binding OrderNumber}"
                                Width="100"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="工单名称"
                                Binding="{Binding OrderName}"
                                Width="100"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="站点名称"
                                Binding="{Binding WorkStationName}"
                                Width="80"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="工艺流程"
                                Binding="{Binding ProcessFlow}"
                                Width="80"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="工序名称"
                                Binding="{Binding ProcessName}"
                                Width="80"
                                IsReadOnly="True"/>
                        <DataGridTextColumn Header="工序编号"
                                Binding="{Binding ProcessNumber}"
                                Width="80"
                                IsReadOnly="True"/>

                        <!-- 任务颜色列 -->
                        <DataGridTemplateColumn Header="任务颜色"
                                Width="80"
                                IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding TaskColor}"
                                            Width="20"
                                            Height="20"
                                            CornerRadius="10"
                                            HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="计划数量"
                                Binding="{Binding PlanQuantity}"
                                Width="80"
                                IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment"
                                            Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="实际生产"
                                Binding="{Binding ActualQuantity}"
                                Width="80"
                                IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment"
                                            Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 分页信息 -->
                <Border Grid.Row="1"
                        Background="#FAFAFA"
                        BorderBrush="#F0F0F0"
                        BorderThickness="0,1,0,0"
                        Padding="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                                Text="{Binding PageInfo}"
                                VerticalAlignment="Center"
                                FontSize="12"/>

                        <!-- 分页控件 -->
                        <StackPanel Grid.Column="1"
                                Orientation="Horizontal"
                                Margin="0,0,20,0">
                            <Button Content="上一页"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Margin="0,0,8,0"
                                    Command="{Binding PreviousPageCommand}"/>
                            <TextBlock Text="{Binding CurrentPage}"
                                    VerticalAlignment="Center"
                                    Margin="8,0"
                                    FontSize="12"
                                    FontWeight="Bold"/>
                            <TextBlock Text="/"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"
                                    FontSize="12"/>
                            <TextBlock Text="{Binding TotalPages}"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"
                                    FontSize="12"/>
                            <Button Content="下一页"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Command="{Binding NextPageCommand}"/>
                        </StackPanel>

                        <!-- 每页条数选择 -->
                        <StackPanel Grid.Column="2"
                                Orientation="Horizontal">
                            <ComboBox Width="60"
                                    Height="32"
                                      ItemsSource="{Binding PageSizeOptions}"
                                      SelectedValue="{Binding PageSize}"
                                      Style="{StaticResource ComboBoxStyle}"/>
                            <TextBlock Text="条/页"
                                    VerticalAlignment="Center"
                                    Margin="8,0,16,0"
                                    FontSize="12"/>
                            <TextBox Width="50"
                                    Height="32"
                                    Text="{Binding GoToPage}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <Button Content="跳转"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Margin="8,0,0,0"
                                    Command="{Binding GoToPageCommand}"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- 加载指示器 -->
        <Grid Grid.Row="3"
                Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                <TextBlock Text="正在加载数据..."
                        FontSize="16"
                        Foreground="White"
                        HorizontalAlignment="Center"/>
                <ProgressBar IsIndeterminate="True"
                        Width="200"
                        Height="4"
                        Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
