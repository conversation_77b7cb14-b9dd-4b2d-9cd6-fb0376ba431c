<UserControl x:Class="WPF_MVVM_Test.MVVM_View.WorkOrderTask.WorkOrderTaskControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.WorkOrderTask"
             mc:Ignorable="d"
             d:DesignHeight="800"
             d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 状态可见性转换器 -->
        <local:StatusToVisibilityConverter x:Key="StatusToVisibilityConverter"/>

        <!-- 状态颜色转换器 -->
        <Style x:Key="StatusTextStyle"
               TargetType="TextBlock">
            <Setter Property="FontWeight"
                    Value="Bold"/>
            <Setter Property="HorizontalAlignment"
                    Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}"
                             Value="未派工">
                    <Setter Property="Foreground"
                            Value="#8C8C8C"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}"
                             Value="已下达">
                    <Setter Property="Foreground"
                            Value="#1890FF"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}"
                             Value="进行中">
                    <Setter Property="Foreground"
                            Value="#52C41A"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}"
                             Value="已完成">
                    <Setter Property="Foreground"
                            Value="#FA8C16"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}"
                             Value="已关闭">
                    <Setter Property="Foreground"
                            Value="#6C757D"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 操作按钮样式 -->
        <Style x:Key="ActionButtonStyle"
               TargetType="Button">
            <Setter Property="Margin"
                    Value="2"/>
            <Setter Property="Padding"
                    Value="8,4"/>
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Background"
                    Value="Transparent"/>
        </Style>

        <!-- 派工按钮样式 -->
        <Style x:Key="StartButtonStyle"
               TargetType="Button"
               BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Content"
                    Value="派工"/>
            <Setter Property="Foreground"
                    Value="#1890FF"/>
            <Setter Property="BorderBrush"
                    Value="#1890FF"/>
        </Style>

        <!-- 开工按钮样式 -->
        <Style x:Key="WorkButtonStyle"
               TargetType="Button"
               BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Content"
                    Value="开工"/>
            <Setter Property="Foreground"
                    Value="#52C41A"/>
            <Setter Property="BorderBrush"
                    Value="#52C41A"/>
        </Style>

        <!-- 完工按钮样式 -->
        <Style x:Key="CompleteButtonStyle"
               TargetType="Button"
               BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Content"
                    Value="完工"/>
            <Setter Property="Foreground"
                    Value="#FA8C16"/>
            <Setter Property="BorderBrush"
                    Value="#FA8C16"/>
        </Style>

        <!-- 查看按钮样式 -->
        <Style x:Key="ViewButtonStyle"
               TargetType="Button"
               BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Content"
                    Value="查看"/>
            <Setter Property="Foreground"
                    Value="#722ED1"/>
            <Setter Property="BorderBrush"
                    Value="#722ED1"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <Border Grid.Row="0"
                Background="#F8F9FA"
                Padding="20"
                Margin="0,0,0,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 第一行搜索条件 -->
                <StackPanel Grid.Row="0"
                            Grid.Column="0"
                            Orientation="Horizontal"
                            Margin="0,0,20,10">
                    <TextBlock Text="请输入任务编号/名称"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"
                               Width="120"/>
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Width="200"
                             Height="32"
                             VerticalContentAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Row="0"
                            Grid.Column="1"
                            Orientation="Horizontal"
                            Margin="0,0,20,10">
                    <TextBlock Text="请选择工序"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"
                               Width="80"/>
                    <ComboBox ItemsSource="{Binding StationOptions}"
                              SelectedItem="{Binding SelectedStation}"
                              Width="150"
                              Height="32"/>
                </StackPanel>

                <StackPanel Grid.Row="0"
                            Grid.Column="2"
                            Orientation="Horizontal"
                            Margin="0,0,20,10">
                    <TextBlock Text="请选择状态"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"
                               Width="80"/>
                    <ComboBox ItemsSource="{Binding StatusOptions}"
                              SelectedItem="{Binding SelectedStatus}"
                              Width="150"
                              Height="32"/>
                </StackPanel>

                <!-- 操作按钮 -->
                <StackPanel Grid.Row="0"
                            Grid.Column="4"
                            Orientation="Horizontal"
                            VerticalAlignment="Center">
                    <Button Content="查询"
                            Command="{Binding SearchCommand}"
                            Background="#1890FF"
                            Foreground="White"
                            Width="80"
                            Height="32"
                            Margin="0,0,10,0"/>
                    <Button Content="重置"
                            Command="{Binding ResetCommand}"
                            Background="#6C757D"
                            Foreground="White"
                            Width="80"
                            Height="32"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1"
                  ItemsSource="{Binding FilteredTasks}"
                  SelectedItem="{Binding SelectedTask}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  IsReadOnly="True"
                  Background="White"
                  RowBackground="White"
                  AlternatingRowBackground="#F8F9FA"
                  BorderBrush="#E9ECEF"
                  BorderThickness="1">

            <DataGrid.Columns>
                <!-- 序号 -->
                <DataGridTextColumn Header="序号"
                                    Binding="{Binding Index}"
                                    Width="60"
                                    ElementStyle="{StaticResource {x:Type TextBlock}}"/>

                <!-- 任务编号 -->
                <DataGridTextColumn Header="任务编号"
                                    Binding="{Binding TaskNumber}"
                                    Width="100"/>

                <!-- 任务名称 -->
                <DataGridTextColumn Header="任务名称"
                                    Binding="{Binding TaskName}"
                                    Width="120"/>

                <!-- 站点名称 -->
                <DataGridTextColumn Header="站点名称"
                                    Binding="{Binding StationName}"
                                    Width="80"/>

                <!-- 计划开工时间 -->
                <DataGridTextColumn Header="计划开工时间"
                                    Width="140">
                    <DataGridTextColumn.Binding>
                        <Binding Path="PlanStartTime"
                                 StringFormat="yyyy-MM-dd HH:mm"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>

                <!-- 计划完工时间 -->
                <DataGridTextColumn Header="计划完工时间"
                                    Width="140">
                    <DataGridTextColumn.Binding>
                        <Binding Path="ActualEndTime"
                                 StringFormat="yyyy-MM-dd HH:mm"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>

                <!-- 计划生产时长 -->
                <DataGridTextColumn Header="计划生产时长"
                                    Binding="{Binding PlanDuration}"
                                    Width="100"/>

                <!-- 实际开工时间 -->
                <DataGridTextColumn Header="实际开工时间"
                                    Width="140">
                    <DataGridTextColumn.Binding>
                        <Binding Path="ActualStartTime"
                                 StringFormat="yyyy-MM-dd HH:mm"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>

                <!-- 实际完工时间 -->
                <DataGridTextColumn Header="实际完工时间"
                                    Width="140">
                    <DataGridTextColumn.Binding>
                        <Binding Path="ActualEndTime"
                                 StringFormat="yyyy-MM-dd HH:mm"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>

                <!-- 实际生产时长 -->
                <DataGridTextColumn Header="实际生产时长"
                                    Binding="{Binding ActualDuration}"
                                    Width="100"/>

                <!-- 状态 -->
                <DataGridTemplateColumn Header="状态"
                                        Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Status}"
                                       Style="{StaticResource StatusTextStyle}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- 操作 -->
                <DataGridTemplateColumn Header="操作"
                                        Width="200">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal"
                                        HorizontalAlignment="Center">
                                <!-- 派工按钮 -->
                                <Button Style="{StaticResource StartButtonStyle}"
                                        Visibility="{Binding Status, Converter={StaticResource StatusToVisibilityConverter}, ConverterParameter='未派工'}"
                                        Command="{Binding DataContext.StartTaskCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"/>

                                <!-- 开工按钮 -->
                                <Button Style="{StaticResource WorkButtonStyle}"
                                        Visibility="{Binding Status, Converter={StaticResource StatusToVisibilityConverter}, ConverterParameter='已下达'}"
                                        Command="{Binding DataContext.StartTaskCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"/>

                                <!-- 完工按钮 -->
                                <Button Style="{StaticResource CompleteButtonStyle}"
                                        Visibility="{Binding Status, Converter={StaticResource StatusToVisibilityConverter}, ConverterParameter='进行中'}"
                                        Command="{Binding DataContext.CompleteTaskCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"/>

                                <!-- 查看按钮 -->
                                <Button Style="{StaticResource ViewButtonStyle}"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页控件 -->
        <Border Grid.Row="2"
                Background="#F8F9FA"
                Padding="20,10"
                BorderBrush="#E9ECEF"
                BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 统计信息 -->
                <StackPanel Grid.Column="0"
                            Orientation="Horizontal"
                            VerticalAlignment="Center">
                    <TextBlock Text="共 "/>
                    <TextBlock Text="{Binding TotalCount}"
                               FontWeight="Bold"
                               Foreground="#1890FF"/>
                    <TextBlock Text=" 条"/>
                </StackPanel>

                <!-- 分页按钮 -->
                <StackPanel Grid.Column="1"
                            Orientation="Horizontal"
                            VerticalAlignment="Center">
                    <Button Content="首页"
                            Command="{Binding FirstPageCommand}"
                            Width="50"
                            Height="30"
                            Margin="0,0,5,0"/>
                    <Button Content="上一页"
                            Command="{Binding PreviousPageCommand}"
                            Width="60"
                            Height="30"
                            Margin="0,0,5,0"/>

                    <TextBlock Text="{Binding CurrentPage}"
                               VerticalAlignment="Center"
                               Margin="10,0"/>
                    <TextBlock Text="/"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalPages}"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>

                    <Button Content="下一页"
                            Command="{Binding NextPageCommand}"
                            Width="60"
                            Height="30"
                            Margin="5,0"/>
                    <Button Content="末页"
                            Command="{Binding LastPageCommand}"
                            Width="50"
                            Height="30"
                            Margin="5,0,0,0"/>

                    <ComboBox SelectedItem="{Binding PageSize}"
                              Margin="20,0,0,0"
                              Width="80"
                              Height="30">
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="20"/>
                        <ComboBoxItem Content="50"/>
                        <ComboBoxItem Content="100"/>
                    </ComboBox>
                    <TextBlock Text="条/页"
                               VerticalAlignment="Center"
                               Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 加载遮罩 -->
        <Grid Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True"
                             Width="200"
                             Height="4"
                             Margin="0,0,0,10"/>
                <TextBlock Text="加载中..."
                           Foreground="White"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
