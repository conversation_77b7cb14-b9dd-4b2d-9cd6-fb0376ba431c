<Window x:Class="WPF_MVVM_Test.MVVM_View.WorkOrderTask.WorkOrderTaskEditDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding DialogTitle}"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="White">

        <Window.Resources>
                <!-- 标签样式 -->
                <Style x:Key="LabelStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="Normal"/>
                        <Setter Property="Foreground"
                                Value="#333333"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                        <Setter Property="Margin"
                                Value="0,0,10,0"/>
                </Style>

                <!-- 必填标签样式 -->
                <Style x:Key="RequiredLabelStyle"
                       TargetType="TextBlock"
                       BasedOn="{StaticResource LabelStyle}">
                        <Setter Property="Foreground"
                                Value="#FF4D4F"/>
                </Style>

                <!-- 输入框样式 -->
                <Style x:Key="InputStyle"
                       TargetType="ComboBox">
                        <Setter Property="Height"
                                Value="36"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="BorderBrush"
                                Value="#D9D9D9"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Padding"
                                Value="8,0"/>
                </Style>

                <!-- 按钮样式 -->
                <Style x:Key="PrimaryButtonStyle"
                       TargetType="Button">
                        <Setter Property="Height"
                                Value="36"/>
                        <Setter Property="MinWidth"
                                Value="80"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Background"
                                Value="#1890FF"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="4"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#40A9FF"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#096DD9"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="SecondaryButtonStyle"
                       TargetType="Button">
                        <Setter Property="Height"
                                Value="36"/>
                        <Setter Property="MinWidth"
                                Value="80"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="Foreground"
                                Value="#666666"/>
                        <Setter Property="BorderBrush"
                                Value="#D9D9D9"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        CornerRadius="4"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#F5F5F5"/>
                                                                <Setter Property="BorderBrush"
                                                                        Value="#1890FF"/>
                                                                <Setter Property="Foreground"
                                                                        Value="#1890FF"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>
        </Window.Resources>

        <Grid Margin="24">
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题区域 -->
                <StackPanel Grid.Row="0"
                            Orientation="Horizontal"
                            Margin="0,0,0,24">
                        <TextBlock Text="{Binding DialogTitle}"
                                   FontSize="20"
                                   FontWeight="Bold"
                                   Foreground="#262626"/>
                </StackPanel>

                <!-- 主要内容区域 -->
                <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 第一行：班组名称和任务负责人 -->
                        <Grid Grid.Row="0"
                              Margin="0,0,0,24">
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                        <StackPanel Orientation="Horizontal"
                                                    Margin="0,0,0,8">
                                                <TextBlock Text="*"
                                                           Style="{StaticResource RequiredLabelStyle}"/>
                                                <TextBlock Text="班组名称"
                                                           Style="{StaticResource LabelStyle}"/>
                                                <Border Background="#1890FF"
                                                        CornerRadius="2"
                                                        Padding="4,2"
                                                        Margin="8,0,0,0">
                                                        <TextBlock Text="1"
                                                                   FontSize="10"
                                                                   Foreground="White"
                                                                   FontWeight="Bold"/>
                                                </Border>
                                        </StackPanel>
                                        <ComboBox ItemsSource="{Binding WorkGroups}"
                                                  SelectedItem="{Binding SelectedWorkGroup}"
                                                  Style="{StaticResource InputStyle}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2">
                                        <StackPanel Orientation="Horizontal"
                                                    Margin="0,0,0,8">
                                                <TextBlock Text="*"
                                                           Style="{StaticResource RequiredLabelStyle}"/>
                                                <TextBlock Text="任务负责人"
                                                           Style="{StaticResource LabelStyle}"/>
                                                <Border Background="#1890FF"
                                                        CornerRadius="2"
                                                        Padding="4,2"
                                                        Margin="8,0,0,0">
                                                        <TextBlock Text="1"
                                                                   FontSize="10"
                                                                   Foreground="White"
                                                                   FontWeight="Bold"/>
                                                </Border>
                                        </StackPanel>
                                        <ComboBox ItemsSource="{Binding Assignees}"
                                                  SelectedItem="{Binding SelectedAssignee}"
                                                  Style="{StaticResource InputStyle}"/>
                                </StackPanel>
                        </Grid>

                        <!-- 第二行：其他成员和质检人员 -->
                        <Grid Grid.Row="1"
                              Margin="0,0,0,24">
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                        <TextBlock Text="其他成员"
                                                   Style="{StaticResource LabelStyle}"
                                                   Margin="0,0,0,8"/>
                                        <Button Height="36"
                                                FontSize="14"
                                                Background="White"
                                                BorderBrush="#D9D9D9"
                                                BorderThickness="1"
                                                Padding="8,0"
                                                HorizontalContentAlignment="Left"
                                                Content="{Binding OtherMembersText}"
                                                Command="{Binding SelectOtherMembersCommand}">
                                                <Button.Template>
                                                        <ControlTemplate TargetType="Button">
                                                                <Border Background="{TemplateBinding Background}"
                                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                                        Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                                          VerticalAlignment="Center"/>
                                                                </Border>
                                                        </ControlTemplate>
                                                </Button.Template>
                                        </Button>
                                        <!-- 测试按钮 -->
                                        <Button Content="测试绑定"
                                                Height="30"
                                                Margin="0,5,0,0"
                                                Background="LightBlue"
                                                Command="{Binding TestCommand}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2">
                                        <TextBlock Text="质检部门"
                                                   Style="{StaticResource LabelStyle}"
                                                   Margin="0,0,0,8"/>
                                        <ComboBox ItemsSource="{Binding QualityDepartments}"
                                                  SelectedItem="{Binding SelectedQualityDepartment}"
                                                  Style="{StaticResource InputStyle}"/>
                                </StackPanel>
                        </Grid>

                        <!-- 第三行：质检人员 -->
                        <Grid Grid.Row="2"
                              Margin="0,0,0,24">
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                        <TextBlock Text="质检人员"
                                                   Style="{StaticResource LabelStyle}"
                                                   Margin="0,0,0,8"/>
                                        <ComboBox ItemsSource="{Binding QualityPersonnels}"
                                                  SelectedItem="{Binding SelectedQualityPersonnel}"
                                                  Style="{StaticResource InputStyle}"/>
                                </StackPanel>

                                <!-- 右侧留空 -->
                                <StackPanel Grid.Column="2">
                                </StackPanel>
                        </Grid>

                        <!-- 第四行：备注 -->
                        <StackPanel Grid.Row="3">
                                <TextBlock Text="备注"
                                           Style="{StaticResource LabelStyle}"
                                           Margin="0,0,0,8"/>
                                <TextBox Text="{Binding Notes}"
                                         Height="120"
                                         FontSize="14"
                                         Background="White"
                                         BorderBrush="#D9D9D9"
                                         BorderThickness="1"
                                         Padding="8"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                </Grid>

                <!-- 底部按钮区域 -->
                <StackPanel Grid.Row="2"
                            Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,24,0,0">
                        <Button Style="{StaticResource PrimaryButtonStyle}"
                                Command="{Binding ConfirmCommand}"
                                Margin="0,0,12,0"
                                Padding="16,0">
                                <Button.Content>
                                        <TextBlock>
                                                <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                                <Setter Property="Text"
                                                                        Value="原检派工"/>
                                                                <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding IsEditMode}"
                                                                                     Value="True">
                                                                                <Setter Property="Text"
                                                                                        Value="保存"/>
                                                                        </DataTrigger>
                                                                </Style.Triggers>
                                                        </Style>
                                                </TextBlock.Style>
                                        </TextBlock>
                                </Button.Content>
                        </Button>
                        <Button Content="取消"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Command="{Binding CancelCommand}"
                                Padding="16,0"/>
                </StackPanel>
        </Grid>
</Window>
